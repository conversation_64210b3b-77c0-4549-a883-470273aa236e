import React, { useEffect, useRef, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { marketService } from '../services/market.service';

interface ForecastChartProps {
  symbol: string;
  timeframe: string;
  onLoad?: (data: any) => void;
}

export const ForecastChart: React.FC<ForecastChartProps> = ({ symbol, timeframe, onLoad }) => {
  const [chartHtml, setChartHtml] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string>(new Date().toLocaleTimeString());
  const chartContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchForecast = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const data = await marketService.getForecast(symbol, timeframe);
        
        if (data.status === 'error') {
          throw new Error(data.error || 'Failed to load forecast');
        }

        setChartHtml(data.chart_html);
        setLastUpdated(new Date(data.generated_at).toLocaleTimeString());
        
        // Call the onLoad callback with the forecast data
        onLoad?.({
          symbol: data.symbol,
          timeframe: data.timeframe,
          currentPrice: data.current_price,
          support: data.support_level,
          resistance: data.resistance_level,
          forecast: data.forecast,
          forecastDates: data.forecast_dates,
          generatedAt: data.generated_at
        });
      } catch (err) {
        console.error('Error fetching forecast:', err);
        setError(err instanceof Error ? err.message : 'Failed to load forecast. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchForecast();
  }, [symbol, timeframe, onLoad]);

  // This effect handles the script execution from the chart HTML
  useEffect(() => {
    if (!chartHtml || !chartContainerRef.current) return;

    // Create a temporary container to parse the HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = chartHtml;

    // Clear the container and append the new content
    if (chartContainerRef.current) {
      chartContainerRef.current.innerHTML = '';
      
      // Clone the nodes to avoid modifying the original HTML
      while (tempDiv.firstChild) {
        chartContainerRef.current.appendChild(tempDiv.firstChild);
      }
      
      // Execute any scripts in the HTML
      const scripts = chartContainerRef.current.getElementsByTagName('script');
      for (let i = 0; i < scripts.length; i++) {
        const script = document.createElement('script');
        script.text = scripts[i].text;
        document.body.appendChild(script).parentNode?.removeChild(script);
      }
    }
  }, [chartHtml]);

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Loading forecast for {symbol}...</CardTitle>
          <p className="text-sm text-muted-foreground">Last updated: {lastUpdated}</p>
        </CardHeader>
        <CardContent>
          <div className="h-96 flex items-center justify-center">
            <div className="animate-pulse text-muted-foreground">Loading chart...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Error Loading Forecast</CardTitle>
          <p className="text-sm text-muted-foreground">Last updated: {lastUpdated}</p>
        </CardHeader>
        <CardContent>
          <div className="h-96 flex items-center justify-center text-destructive">
            <p>{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>{symbol} Price Forecast</CardTitle>
            <p className="text-sm text-muted-foreground">
              Last updated: {lastUpdated}
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div ref={chartContainerRef} className="w-full" style={{ minHeight: '500px' }} />
      </CardContent>
    </Card>
  );
};

export default ForecastChart;
