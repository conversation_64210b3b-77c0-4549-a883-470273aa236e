import { useEffect, useState, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

export default function OAuthCallback() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { handleOAuthCallback } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [errorDetails, setErrorDetails] = useState<any>(null);
  const hasProcessed = useRef(false);

  useEffect(() => {
    // Prevent multiple executions in development with StrictMode
    if (hasProcessed.current) return;
    
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const errorParam = searchParams.get('error');

    console.log('OAuth Callback - URL Params:', { code, state, error: errorParam });

    const processCallback = async () => {
      if (hasProcessed.current) return;
      hasProcessed.current = true;
      
      setIsLoading(true);
      
      try {
        // Handle OAuth errors from provider
        if (errorParam) {
          const errorDescription = searchParams.get('error_description') || 'Authentication failed';
          const errorMsg = `OAuth Error: ${errorParam} - ${errorDescription}`;
          console.error('OAuth provider error:', { error: errorParam, description: errorDescription });
          setError(errorMsg);
          setErrorDetails({ error: errorParam, description: errorDescription });
          return;
        }

        // Check for required parameters
        if (!code) {
          const errorMsg = 'No authorization code received from the OAuth provider';
          console.error(errorMsg);
          setError(errorMsg);
          return;
        }

        console.log('Exchanging authorization code for tokens...');
        const redirectUri = `${window.location.origin}/auth/callback`;
        
        console.log('Sending request to backend with:', { 
          code, 
          state, 
          redirect_uri: redirectUri 
        });

        // Call the backend to exchange the authorization code for tokens
        const response = await fetch('http://localhost:5000/api/auth/callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify({
            code,
            state,
            redirect_uri: redirectUri,
          }),
          credentials: 'include',
        });

        console.log('Backend response status:', response.status);
        
        // Handle non-JSON responses
        const contentType = response.headers.get('content-type');
        let responseData;
        
        try {
          responseData = contentType?.includes('application/json')
            ? await response.json()
            : { message: await response.text() };
        } catch (e) {
          console.error('Error parsing response:', e);
          responseData = { message: 'Invalid response from server' };
        }

        console.log('Backend response data:', responseData);
        
        // Log the complete response data structure
        console.group('OAuth Response Data');
        console.log('Complete response data:', responseData);
        
        if (responseData.user) {
          console.log('User object structure:', {
            id: responseData.user.id,
            email: responseData.user.email,
            username: responseData.user.username,
            firstName: responseData.user.firstName,
            lastName: responseData.user.lastName,
            avatar: responseData.user.avatar,
            picture: responseData.user.picture,
            provider: responseData.user.provider,
            // Check for nested profile data
            profile: responseData.user.profile,
            // Check for any image URLs in the user object
            imageUrl: responseData.user.imageUrl,
            // Complete raw data
            rawData: responseData.user
          });
          
          // Log if we have any potential image URLs
          const imageUrls = [
            responseData.user.picture,
            responseData.user.avatar,
            responseData.user.imageUrl,
            responseData.user.profile?.picture,
            responseData.user.profile?.imageUrl
          ].filter(Boolean);
          
          console.log('Potential image URLs found:', imageUrls);
        } else {
          console.warn('No user data found in OAuth response');
        }
        console.groupEnd();

        if (!response.ok) {
          const errorMsg = responseData?.message || 
                         responseData?.error || 
                         `Server returned ${response.status} status`;
          const errorDetails = responseData.details || responseData;
          
          console.error('Authentication failed:', {
            status: response.status,
            error: errorMsg,
            details: errorDetails
          });
          
          throw new Error(errorMsg, { cause: errorDetails });
        }

        // Handle successful authentication
        console.log('Authentication successful, processing response...');
        
        // Handle the authentication result
        if (responseData.requires_2fa) {
          console.log('2FA required, redirecting to verification page');
          navigate('/verify-2fa', { 
            state: { 
              tempToken: responseData.temp_token,
              user: responseData.user 
            } 
          });
        } else {
          console.log('Authentication complete, storing tokens and redirecting...');
          await handleOAuthCallback(responseData);
          navigate('/');
        }
      } catch (error) {
        console.error('Authentication error:', error);
        
        // Extract error details from the error object
        const errorMessage = error instanceof Error 
          ? error.message 
          : 'An unexpected error occurred during authentication';
          
        const details = error instanceof Error && 'cause' in error 
          ? error.cause 
          : null;
        
        setError(errorMessage);
        setErrorDetails(details || 'No additional details available');
      } finally {
        setIsLoading(false);
      }
    };

    processCallback();
  }, [searchParams, navigate, handleOAuthCallback]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-background p-4">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
        <h2 className="text-xl font-semibold mb-2">Authenticating...</h2>
        <p className="text-muted-foreground text-center">Please wait while we complete your authentication.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-4">
        <div className="w-full max-w-md bg-card rounded-lg shadow-md overflow-hidden">
          <div className="p-6">
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-destructive/10 text-destructive mb-4 mx-auto">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
            </div>
            <h2 className="text-2xl font-semibold text-center mb-2">Authentication Error</h2>
            <p className="text-muted-foreground text-center mb-6">{error}</p>
            
            {errorDetails && (
              <div className="bg-muted/50 p-4 rounded-md mb-6 text-sm overflow-x-auto">
                <pre className="whitespace-pre-wrap break-words">
                  {typeof errorDetails === 'string' 
                    ? errorDetails 
                    : JSON.stringify(errorDetails, null, 2)}
                </pre>
              </div>
            )}
            
            <div className="space-y-3">
              <button
                onClick={() => navigate('/login')}
                className="w-full px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
              >
                Return to Login
              </button>
              <button
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-md transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
        <h2 className="text-2xl font-semibold mb-2">Completing Authentication</h2>
        <p className="text-muted-foreground">Please wait while we log you in...</p>
      </div>
    </div>
  );
}
