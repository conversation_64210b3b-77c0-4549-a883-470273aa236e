{"extends": "./tsconfig.json", "compilerOptions": {"types": ["node", "jest"], "esModuleInterop": true, "module": "ESNext", "target": "ES2022", "moduleResolution": "node", "allowJs": true, "strict": true, "noEmit": false, "outDir": "./dist-test", "sourceMap": true, "skipLibCheck": true, "jsx": "react-jsx"}, "include": ["src/**/*.test.ts", "src/**/*.test.tsx", "src/**/*.spec.ts", "test-*.ts", "**/*.d.ts"], "exclude": ["node_modules", "dist"]}