export default {
  "app": {
    "name": "DeepTrade",
    "tagline": "AI-Powered Cryptocurrency Trading",
    "description": "Advanced trading signals and automated portfolio management",
  },
  "navigation": {
    "dashboard": "Dashboard",
    "trading": "Trading",
    "signals": "Signals",
    "apiCredentials": "API Credentials",
    "autoTrading": "Auto Trading",
    "tierManagement": "Tier Management",
    "referrals": "Referrals",
    "accessSecurity": "Access & Security",
    "settings": "Settings",
    "help": "Help & Support",
    "login": "Login",
    "register": "Register",
    "logout": "Logout",
    "home": "Home",
  },
  "auth": {
    "login": {
      "title": "Welcome Back",
      "subtitle": "Enter your email and password to sign in",
      "email": "Email",
      "password": "Password",
      "emailPlaceholder": "<EMAIL>",
      "passwordPlaceholder": "••••••••",
      "rememberMe": "Remember me",
      "forgotPassword": "Forgot password?",
      "signIn": "Sign In",
      "noAccount": "Don't have an account?",
      "signUp": "Sign up",
      "googleSignIn": "Continue with Google",
      "orContinueWith": "Or continue with",
      "fillAllFields": "Please fill in all fields",
      "loginSuccess": "Successfully logged in!",
      "loginFailed": "Login failed",
      "loginFailedDescription": "Failed to log in",
    },
    "register": {
      "title": "Create Account",
      "subtitle": "Enter your information to create an account",
      "firstName": "First Name",
      "lastName": "Last Name",
      "email": "Email",
      "password": "Password",
      "confirmPassword": "Confirm Password",
      "agreeTerms": "By clicking continue, you agree to our",
      "createAccount": "Create account",
      "hasAccount": "Already have an account?",
      "signIn": "Sign in",
      "orContinueWith": "Or continue with",
      "googleSignUp": "Sign up with Google",
      "termsOfService": "Terms of Service",
      "privacyPolicy": "Privacy Policy",
    },
    "passwordRequirements": {
      "title": "Password Requirements",
      "length": "At least 8 characters",
      "uppercase": "One uppercase letter",
      "lowercase": "One lowercase letter",
      "number": "One number",
      "special": "One special character",
    },
    "errors": {
      "invalidCredentials": "Invalid email or password",
      "emailRequired": "Email is required",
      "passwordRequired": "Password is required",
      "passwordTooShort": "Password must be at least 8 characters",
      "passwordsNotMatch": "Passwords do not match",
      "emailInvalid": "Please enter a valid email address",
      "termsRequired": "You must agree to the terms and conditions",
    }
  },
  "dashboard": {
    "title": "Dashboard",
    "welcome": "Welcome back!",
    "overview": "Overview",
    "trading": "Trading",
    "tier": "Tier",
    "balance": "Balance",
    "pnlToday": "P&L Today",
    "totalTrades": "Total Trades",
    "winRate": "Win Rate",
    "activeSignals": "Active Signals",
    "tradingOverview": "Trading Overview",
    "quickActions": "Quick Actions",
    "viewSignals": "View Signals",
    "autoTrading": "Auto Trading",
    "autoTradingDescription": "Automatically execute trading signals",
    "currentTier": "Current Tier",
    "progressTo": "Progress to Tier {{tier},",
    "yourBenefits": "Your Benefits",
    "advancedSignals": "Advanced Trading Signals",
    "autoTradingFeatures": "Auto-Trading Features",
    "prioritySupport": "Priority Support",
    "monthlyRate": "Monthly Rate",
    "profitShare30": "30% profit share",
    "profitShare20": "20% profit share",
    "profitShare10": "10% profit share",
    "verifyingNFT": "Verifying NFT ownership...",
    "debt": "Debt",
    "btcPrice": {
      "loading": "BTC/USDT: Loading...",
      "unavailable": "BTC/USDT: --",
      "connecting": "Connecting...",
      "live": "Live",
      "error": "Error ({{count}})",
      "24h": "24h",
    },
    "forecast": {
      "title": "Price Forecast",
      "refresh": "Refresh",
      "errorLoading": "Error loading chart",
      "retry": "Retry",
      "noData": "No chart data available",
    },
    "riskSettings": {
      "title": "Risk Management Settings",
      "paperTradingTitle": "Risk Management Settings - Paper Trading",
      "loading": "Loading risk settings...",
      "investmentPercentage": "Investment Percentage",
      "investmentDescription": "Percentage of balance to use for trading (0% - 10%)",
      "leverage": "Leverage",
      "leverageDescription": "Max: {{max}}x for Tier {{tier}} + {{exchange}} {{market},",
      "paperTradingLeverageDesc": "In paper mode, you can use any leverage setting for testing purposes",
      "exchange": "Exchange: ",
      "configureCredentials": "Configure in API Credentials",
      "warningZeroInvestment": "Select investment percentage > 0% to enable trading",
      "paperTradingNote": "Paper trading mode - no tier limitations apply",
      "saveSettings": "Save Settings",
      "saving": "Saving...",
      "resetToSafe": "Reset to Safe",
      "confirmSaveTitle": "Confirm Risk Settings",
      "confirmSaveDesc": "Are you sure you want to save these risk settings?\n\nInvestment: {{investment}}%\nLeverage: {{leverage}}x\nMode: {{mode}}",
      "confirmSave": "Save Settings",
      "saveSuccess": "Settings Saved",
      "saveSuccessDesc": "Your risk management settings have been saved successfully.",
      "saveError": "Save Failed",
    },
    "activePositions": {
      "title": "Active Positions",
      "tabs": {
        "all": "all",
        "app": "app",
        "external": "external",
      },
      "refresh": "Refresh",
      "errorLoading": "Error loading active positions",
      "retry": "Retry",
      "noPositions": "No active positions.",
      "noPositionsDesc": "Your active trading positions will appear here once you start trading or enable auto-trading.",
      "noAppPositions": "No active app positions.",
      "noExternalPositions": "No active external positions.",
      "addCredentialsWarning": "⚠️ Add API credentials to see external positions",
      "externalError": "❌ External positions error: {{error},",
      "externalErrorGeneric": "❌ {{error},",
      "diagnostics": "App: {{app}} | External: {{external},",
      "fields": {
        "size": "Size",
        "entryPrice": "Entry Price",
        "currentPrice": "Current Price",
        "margin": "Margin",
        "liquidationPrice": "Liq. Price",
        "marginRate": "Margin Rate",
        "takeProfit": "TP",
        "stopLoss": "SL",
        "roi": "ROI",
        "none": "None",
        "na": "N/A",
      },
      "positionTypes": {
        "appPositions": "App Positions",
        "externalPositions": "External Positions",
      }
    },
    "profitShare": {
      "title": "Profit Share Tracking",
      "loading": "Loading profit share data...",
      "noData": "No profit share data available. Start trading to track your profits.",
      "balanceOverview": "Balance Overview",
      "initialBalance": "Initial Balance:",
      "currentBalance": "Current Balance:",
      "netDeposits": "Net Deposits:",
      "profitAnalysis": "Profit Analysis",
      "tradingProfit": "Trading Profit:",
      "trueProfit": "True Profit:",
      "profitPercentage": "Profit %:",
      "profitShareInfo": "Profit Share",
      "tierRate": "Tier {{tier}} Rate:",
      "amountOwed": "Amount Owed:",
      "status": "Status:",
      "profitable": "Account is in profit - Profit share applies to gains above initial balance",
      "notProfitable": "Account is not yet profitable - No profit share until balance exceeds initial + deposits",
      "paymentRequired": "Payment Required",
      "paymentDescription": "You have ${{amount}} in profit share fees to pay.",
      "payNow": "Pay Now",
    },
    "tradingHistory": {
      "title": "Trading History (App Trades Only)",
      "description": "Showing trades initiated by DeepTrade platform",
      "loading": "Loading...",
      "headers": {
        "date": "Date",
        "symbol": "Symbol",
        "side": "Side",
        "size": "Size",
        "entry": "Entry",
        "exit": "Exit",
        "pnl": "PnL",
        "status": "Status",
      },
      "noHistory": "No trading history found. Start trading to see your history here.",
      "pagination": "Page {{page}} of {{pages}} ({{total}} total trades)",
      "previous": "Previous",
      "next": "Next",
    },
    "blockedUser": {
      "title": "Trading Blocked:",
      "message": "Unpaid profit share for your tier ({{tier}}). Owed: ${{owed}}. Please settle your balance to continue.",
    }
  },
  "trading": {
    "title": "Trading",
    "signals": "Signals",
    "positions": "Positions",
    "history": "History",
    "autoTrading": "Auto Trading",
    "autoTradingActive": "Auto Trading Active",
    "autoTradingDisabled": "Auto Trading Disabled",
    "autoTradingActiveDesc": "Signals will be executed automatically",
    "autoTradingDisabledDesc": "Enable to automatically execute trading signals",
    "noActiveSignals": "No Active Signals",
    "waitingForSignals": "Waiting for new trading opportunities...",
    "enableAutoTrading": "Enable auto trading to receive signals",
    "refreshSignals": "Refresh Signals",
    "noOpenPositions": "No Open Positions",
    "positionsDesc": "Your active trading positions will appear here",
    "noTradingHistory": "No Trading History",
    "historyDesc": "Your completed trades will appear here",
    "executeTradeButton": "Execute Trade",
    "viewDetailsButton": "View Details",
    "entry": "Entry",
    "stopLoss": "Stop Loss",
    "takeProfit": "Take Profit",
    "confidence": "{{value}}% confidence",
    "strategy": "Strategy",
    "firstTp": "First TP",
    "secondTp": "Second TP",
    "autoMoveStopLoss": "Auto-move SL to breakeven after first TP",
    "tradingMode": "Trading Mode",
    "paperMode": "Paper",
    "liveMode": "Live",
    "paperTradingActive": "Paper Trading Active",
    "liveTradingActive": "Live Trading Active",
    "paperTradingDesc": "Practice trading with virtual funds",
    "liveTradingDesc": "Trade with real money",
    "virtualBalance": "Virtual Balance",
    "paperTradingHelp": "Paper Trading Help",
    "resetAccount": "Reset Account",
    "paperTradingGuide": "Paper Trading Guide",
    "paperModeTitle": "Paper Mode",
    "noProfitSharePaper": "No profit share in paper mode",
    "paperTradingPositions": "Paper Trading Positions",
    "paperTradingHistory": "Paper Trading History",
    "simulatedTradingHistory": "Simulated trading history",
    "noPaperPositions": "No paper trading positions",
    "enableAutoTradingPaper": "Paper trading will automatically generate simulated positions based on market signals",
    "paperTradingMode": "Paper Trading Mode",
    "autoTradingNotAvailable": "Auto-Trading Not Available",
    "autoTradingDisabledPaper": "Auto-trading controls live trading. Paper trading generates signals automatically.",
    "riskManagement": "Risk Management",
    "riskSettings": "Risk Settings",
    "investmentPercentage": "Investment Percentage",
    "leverage": "Leverage",
    "maxLeverage": "Max Leverage",
    "currentTier": "Current Tier",
    "exchange": "Exchange",
    "accountType": "Account Type",
    "marketType": "Market Type",
    "saveSettings": "Save Settings",
    "resetToSafe": "Reset to Safe",
    "saving": "Saving...",
    "riskSettingsSaved": "Risk settings saved successfully",
    "failedToSaveRiskSettings": "Failed to save risk settings",
    "position": "position",
    "internal": "Internal",
    "external": "External",
    "marketClose": "MKT Close",
    "reverse": "Reverse",
    "editTpSl": "TP/SL",
    "refreshActivePositions": "Refresh Active Positions",
    "youHaveNoPosition": "You have no position.",
    "noActiveAppPositions": "No active app positions found.",
    "noActiveExternalPositions": "No active external positions found.",
    "yourActivePositionsWillAppear": "Your active trading positions will appear here.",
    "previous": "Previous",
    "next": "Next",
    "learnMore": "Learn More",
    "paperTradingLabel": "Paper Trading",
    "recentPaperTrades": "Recent Paper Trades",
    "noPaperTrades": "No paper trades yet",
    "paperTradingAutoGenerate": "Paper trading will automatically generate simulated trades based on market signals",
    "activePositionsTitle": "Active Positions",
    "youHaveNoPositions": "You have no position.",
    "yourActivePositionsWillAppearHere": "Your active trading positions will appear here once you start trading or enable auto-trading.",
    "max": "Max",
  },
  "common": {
    "loading": "Loading...",
    "error": "Error",
    "success": "Success",
    "warning": "Warning",
    "info": "Information",
    "confirm": "Confirm",
    "cancel": "Cancel",
    "save": "Save",
    "edit": "Edit",
    "delete": "Delete",
    "close": "Close",
    "back": "Back",
    "next": "Next",
    "previous": "Previous",
    "submit": "Submit",
    "reset": "Reset",
    "search": "Search",
    "filter": "Filter",
    "sort": "Sort",
    "refresh": "Refresh",
    "enable": "Enable",
    "disable": "Disable",
    "active": "Active",
    "inactive": "Inactive",
    "toggleOn": "ON",
    "toggleOff": "OFF",
    "yes": "Yes",
    "no": "No",
    "ok": "OK",
    "apply": "Apply",
    "clear": "Clear",
    "select": "Select",
    "upload": "Upload",
    "download": "Download",
    "copy": "Copy",
    "copied": "Copied!",
    "share": "Share",
    "print": "Print",
    "export": "Export",
    "import": "Import",
    "and": "and",
    "on": "ON",
    "off": "OFF",
    "enabled": "enabled",
    "disabled": "disabled",
    "for": "for",
  },
  "wallet": {
    "connect": "Connect Wallet",
    "disconnect": "Disconnect",
    "connected": "Connected",
    "notConnected": "Not Connected",
    "connecting": "Connecting...",
    "balance": "Balance",
    "address": "Address",
    "solanaBlockchain": "Solana Blockchain Powered",
    "solanaTooltip": "Built on Solana - Fast, secure, and scalable blockchain",
    "wrongNetwork": "Wrong Network",
    "switchNetwork": "Switch Network",
    "transactionPending": "Transaction Pending",
    "transactionConfirmed": "Transaction Confirmed",
    "transactionFailed": "Transaction Failed",
    "connectionError": "Connection Error",
    "connectionFailed": "Failed to connect wallet",
    "connectionRejected": "Wallet connection was rejected",
    "walletNotFound": "No Solana wallet found. Please install a wallet like Phantom.",
    "disconnected": "Wallet Disconnected",
    "disconnectedSuccess": "Your wallet has been disconnected successfully.",
    "disconnectError": "Disconnect Error",
    "disconnectFailed": "Failed to disconnect wallet.",
    "confirmDisconnect": "Confirm Wallet Disconnect",
    "confirmDisconnectMessage": "Are you sure you want to disconnect your wallet?",
  },
  "notifications": {
    "autoTradingEnabled": "Auto-trading has been enabled",
    "autoTradingDisabled": "Auto-trading has been disabled",
    "settingsSaved": "Settings have been saved successfully",
    "passwordChanged": "Password changed successfully",
    "profileUpdated": "Profile updated successfully",
    "walletConnected": "Wallet connected successfully",
    "walletDisconnected": "Wallet disconnected",
    "signalExecuted": "Trading signal executed",
    "tradeCompleted": "Trade completed successfully",
    "paymentSuccessful": "Payment processed successfully",
    "tierUpgraded": "Tier upgraded successfully",
    "autoTrading": {
      "enabledTitle": "Auto-Trading Enabled",
      "enabledDescription": "Auto-trading has been enabled successfully. Balance requirement verified.",
      "disabledTitle": "Auto-Trading Disabled",
      "disabledDescription": "Auto-trading has been disabled successfully.",
      "insufficientBalanceTitle": "Insufficient Balance",
      "insufficientBalanceDescription": "Minimum balance of {{required}} USDT required. Your current balance is {{current}} USDT.",
      "missingCredentialsTitle": "Missing API Credentials",
      "missingCredentialsDescription": "Please add and validate your exchange API credentials first.",
      "balanceVerificationFailedTitle": "Balance Verification Failed",
      "balanceVerificationFailedDescription": "Failed to verify account balance. Please check your API credentials.",
      "errorTitle": "Auto-Trading Error",
      "errorDescription": "Failed to toggle auto-trading. Please try again.",
    }
  },
  "errors": {
    "generic": "Something went wrong. Please try again.",
    "network": "Network error. Please check your connection.",
    "unauthorized": "You are not authorized to perform this action.",
    "forbidden": "Access denied.",
    "notFound": "The requested resource was not found.",
    "serverError": "Server error. Please try again later.",
    "validationError": "Please check your input and try again.",
    "walletNotConnected": "Please connect your wallet first.",
    "insufficientBalance": "Insufficient balance.",
    "transactionFailed": "Transaction failed. Please try again.",
    "apiError": "API error. Please contact support if this persists.",
  },
  "settings": {
    "title": "Settings",
    "profile": {
      "title": "Profile Information",
      "description": "Update your account profile information.",
      "firstName": "First Name",
      "lastName": "Last Name",
      "email": "Email Address",
      "updateProfile": "Update Profile",
      "profileUpdated": "Profile updated successfully",
    },
    "security": {
      "title": "Security Settings",
      "description": "Manage your account security and authentication.",
      "currentPassword": "Current Password",
      "newPassword": "New Password",
      "confirmPassword": "Confirm New Password",
      "changePassword": "Change Password",
      "passwordChanged": "Password changed successfully",
      "enable2FA": "Enable Two-Factor Authentication",
      "disable2FA": "Disable Two-Factor Authentication",
      "twoFactorEnabled": "Two-factor authentication enabled",
      "twoFactorDisabled": "Two-factor authentication disabled",
    },
    "language": {
      "title": "Language & Preferences",
      "description": "Customize your language and display preferences.",
      "selectLanguage": "Language",
      "languageDescription": "Choose your preferred language for the interface.",
    },
    "account": {
      "title": "Account Management",
      "description": "Manage your account settings and data.",
      "deleteAccount": "Delete Account",
      "deleteAccountWarning": "This action cannot be undone. This will permanently delete your account and remove your data from our servers.",
      "confirmDelete": "Type 'DELETE' to confirm",
      "accountDeleted": "Account deleted successfully",
      "deactivated": {
        "title": "Account Deactivated",
        "message": "Your account has been deactivated and access to trading features is restricted.",
        "nextSteps": "What to do next:",
        "step1": "Contact our support team for assistance",
        "step2": "Provide your account details and reason for deactivation",
        "step3": "Wait for account review and reactivation",
        "emailSupport": "Email Support",
        "note": "Note:",
        "noteText": "Your account data is preserved and will be restored upon reactivation. Trading activities are temporarily suspended for security reasons."
      }
    }
  },
  "accessSecurity": {
    "title": "Access & Security",
    "subtitle": "Monitor your account security and login activity",
    "totalLogins": "Total Logins",
    "successRate": "Success Rate",
    "uniqueIPs": "Unique IPs",
    "twoFactorStatus": "2FA Status",
    "enabled": "Enabled",
    "disabled": "Disabled",
    "currentSession": "Current Session",
    "currentSessionDescription": "Information about your current login session",
    "recentLoginActivity": "Recent Login Activity",
    "recentLoginDescription": "Your recent login attempts and sessions",
    "refresh": "Refresh",
    "ipAddress": "IP Address",
    "timestamp": "Timestamp",
    "status": "Status",
    "userAgent": "User Agent",
    "successful": "Successful",
    "failed": "Failed",
    "noLoginActivity": "No login activity found",
    "currentDevice": "Current Device",
    "location": "Location",
    "sessionStart": "Session Start",
  },
  "tiers": {
    "title": "Tier Management",
    "currentTier": "Current Tier",
    "choosePlan": "Choose Your Plan",
    "tier1": {
      "name": "Tier 1",
      "subtitle": "Free",
      "description": "Perfect for getting started with basic trading signals",
      "features": [
        "Basic trading signals",
        "Manual trade execution",
        "Community support",
        "Basic market analysis"
      ]
    },
    "tier2": {
      "name": "Tier 2",
      "subtitle": "30 days access",
      "description": "Advanced features for serious traders",
      "features": [
        "Advanced AI trading signals",
        "Auto-trading capabilities",
        "Priority support",
        "Advanced analytics",
        "Risk management tools"
      ]
    },
    "tier3": {
      "name": "Tier 3",
      "subtitle": "NFT Required",
      "description": "Premium tier with exclusive features",
      "features": [
        "Premium trading signals",
        "Advanced auto-trading",
        "VIP support",
        "Exclusive market insights",
        "Custom strategies"
      ]
    },
    "activate": "Activate",
    "current": "Current",
    "paymentRequired": "Payment Required",
    "nftRequired": "NFT Required",
    "membershipStatus": "Membership Status",
    "active": "Active",
    "expired": "Expired",
    "daysRemaining": "Days remaining",
    "expiresOn": "Expires on",
  },
  "apiCredentials": {
    "title": "API Credentials",
    "description": "Manage your exchange API credentials for trading",
    "addCredentials": "Add API Credentials",
    "exchange": "Exchange",
    "exchanger": "Exchanger",
    "apiKey": "API Key",
    "apiSecret": "API Secret",
    "secretKey": "Secret Key",
    "passphrase": "Passphrase",
    "testConnection": "Test Connection",
    "save": "Save Credentials",
    "edit": "Edit",
    "delete": "Delete",
    "status": "Status",
    "connected": "Connected",
    "disconnected": "Disconnected",
    "invalid": "Invalid",
    "active": "Active",
    "testing": "Testing...",
    "connectionSuccess": "Connection successful",
    "connectionFailed": "Connection failed",
    "credentialsAdded": "API credentials added successfully",
    "credentialsUpdated": "API credentials updated successfully",
    "credentialsDeleted": "API credentials deleted successfully",
    "credentialAddedLimitReached": "Credential added (limit reached)",
    "requiredMarketType": "Required Market Type",
    "connectedTo": "Connected to",
    "connectionNeedsAttention": "Connection to",
    "needsAttention": "needs attention",
    "lastUpdated": "Last updated",
    "updateCredentialsMessage": "Please update your API credentials to continue trading",
    "disconnect": "Disconnect",
    "disconnecting": "Disconnecting...",
    "setAsActive": "Set as Active",
    "activating": "Activating...",
    "autoTrading": "Auto Trading",
    "enableAutoTradingDesc": "Enable automated trading for this exchange",
    "tier2LimitReached": "Tier 2 limit reached: up to two CEX credentials allowed.",
    "add": "Add",
    "update": "Update",
    "connectionValidated": "Connection validated!",
    "autoTradingStatusChanged": "Auto trading has been",
    "marketTypes": {
      "futures": "Futures",
      "spot": "Spot",
      "binanceNote": "Futures trading - leverage up to 125x available",
      "binanceUsNote": "Spot trading with margin - leverage up to 3x available",
      "krakenNote": "Futures trading - leverage up to 100x available",
      "bybitNote": "Futures trading - leverage up to 100x available",
      "hyperliquidNote": "Perpetual futures trading - leverage up to 50x available",
      "standardNote": "Standard spot trading",
    },
    "errors": {
      "loadFailed": "Failed to load credentials",
      "keysRequired": "API Key and Secret Key are required",
      "tierUndetermined": "Unable to determine user tier",
      "tier1Limit": "Tier 1 allows only one API credential",
      "tier2PerCex": "Tier 2 allows only one credential per CEX",
      "tier2Limit": "Tier 2 allows up to two credentials (one per CEX)",
      "tier3Limit": "Tier 3 allows only one credential for all CEXs",
      "saveFailed": "Save failed",
      "validationFailed": "Validation failed",
      "activationFailed": "Activation failed",
      "deactivationFailed": "Deactivation failed",
      "noCredentials": "No credentials found for this exchange",
      "autoTradingUpdateFailed": "Auto Trading Update Failed",
      "autoTradingUpdateFailedDesc": "Failed to update auto trading settings",
    },
    "success": {
      "credentialsSaved": "Credentials saved",
      "connectionSuccessful": "Connection successful",
      "activated": "Activated",
      "deactivated": "Deactivated",
      "autoTradingEnabled": "Auto Trading Enabled",
      "autoTradingDisabled": "Auto Trading Disabled",
    }
  },
  "referrals": {
    "title": "Referral Dashboard",
    "yourReferralCode": "Your Referral Code",
    "referralLink": "Referral Link",
    "copyLink": "Copy Link",
    "linkCopied": "Link copied to clipboard",
    "totalReferrals": "Total Referrals",
    "activeReferrals": "Active Referrals",
    "totalEarnings": "Total Earnings",
    "pendingPayouts": "Pending Payouts",
    "referralHistory": "Referral History",
    "noReferrals": "No referrals yet",
    "inviteFriends": "Invite friends and earn rewards",
  },
  "help": {
    "title": "Help & Support",
    "faq": "FAQ",
    "legal": "Legal",
    "contact": "Contact",
    "searchFAQ": "Search FAQ...",
    "categories": {
      "general": "General",
      "trading": "Trading",
      "tiers": "Tiers",
      "setup": "Setup",
      "security": "Security",
      "billing": "Billing",
    },
    "contactSupport": "Contact Support",
    "email": "Email",
    "subject": "Subject",
    "message": "Message",
    "sendMessage": "Send Message",
    "messageSent": "Message sent successfully",
  },
  "paperTradingHelp": {
    "title": "Paper Trading Guide",
    "whatIsPaperTrading": "What is Paper Trading?",
    "whatIsPaperTradingDesc": "Paper trading is a simulated trading environment that allows you to practice trading strategies using virtual money instead of real funds. It's the perfect way to test our AI trading system without any financial risk.",
    "keyBenefits": "Key Benefits",
    "riskFree": "Risk-Free Learning",
    "riskFreeDesc": "Practice with virtual funds - no real money at risk",
    "realMarketData": "Real Market Data",
    "realMarketDataDesc": "Experience live market conditions and price movements",
    "aiTradingSystem": "AI Trading System",
    "aiTradingSystemDesc": "Test our advanced AI signals in a safe environment",
    "buildConfidence": "Build Confidence",
    "buildConfidenceDesc": "Develop your trading skills before using real money",
    "howItWorks": "How It Works",
    "step1": "Toggle to Paper Mode",
    "step1Desc": "Switch the trading mode toggle to enable paper trading",
    "step2": "Virtual Balance",
    "step2Desc": "Start with $10,000 virtual balance (can be reset)",
    "step3": "AI Signals",
    "step3Desc": "Our AI generates trading signals based on real market data",
    "step4": "Simulated Trades",
    "step4Desc": "Trades are executed virtually - track your performance",
    "importantNotes": "Important Notes",
    "noRealMoney": "No Real Money",
    "noRealMoneyDesc": "Paper trading uses virtual funds only. No real profits or losses occur.",
    "noRealFees": "No Real Fees",
    "noRealFeesDesc": "Trading fees and slippage are simulated for realistic experience.",
    "performanceTracking": "Performance Tracking",
    "performanceTrackingDesc": "All statistics and analytics are based on simulated trades only.",
    "readyToStart": "Ready to Get Started?",
    "readyToStartDesc": "Toggle the \"Trading Mode\" switch to \"Paper\" and start practicing with virtual funds today!",
    "buildConfidenceFooter": "💪 Build confidence with paper trading before risking real money!",
    "resetModal": {
      "title": "Reset Paper Trading Account",
      "warning": "Warning: This action cannot be undone",
      "warningDesc": "This will permanently delete all your paper trading history and reset your virtual balance.",
      "resetLimitReached": "Daily Reset Limit Reached",
      "resetLimitReachedDesc": "You have used all 3 resets for today. Limit resets at midnight UTC.",
      "resetsRemaining": "Resets remaining today: {{count},",
      "currentBalance": "Current Balance: ${{balance},",
      "resetToDefault": "Reset to default $10,000",
      "customBalance": "Set custom balance",
      "customBalanceDesc": "Enter a custom starting balance (minimum $1,000)",
      "confirmationRequired": "Type 'RESET' to confirm",
      "confirmationPlaceholder": "Type RESET here...",
      "confirmationRequiredError": "Please type 'RESET' to confirm",
      "invalidBalance": "Invalid Balance",
      "invalidBalanceDesc": "Please enter a valid positive balance amount",
      "resetting": "Resetting...",
      "resetButton": "Reset Account",
    },
    "howItWorksModal": {
      "title": "How Paper Trading Works",
      "step1": "Start with $10,000 virtual balance (configurable)",
      "step2": "Enable auto-trading to let AI execute trades automatically",
      "step3": "Monitor performance and adjust risk settings as needed",
      "step4": "Switch to live trading when you're confident in the system",
    },
    "importantNotesDetails": {
      "noRealMoneyTitle": "No Real Money:",
      "noRealMoneyDesc": "Paper trading uses virtual funds only. No real profits or losses occur.",
      "noFeesTitle": "No Fees:",
      "noFeesDesc": "Paper trading doesn't generate profit-sharing fees since no real profits are made.",
      "resetLimitTitle": "Reset Limit:",
      "resetLimitDesc": "You can reset your paper account up to 3 times per day.",
    },
    "benefits": {
      "title": "Benefits of Paper Trading",
      "riskFreeLearning": "Risk-Free Learning",
      "riskFreeLearningDesc": "Practice without losing real money while learning how our AI system works",
      "strategyTesting": "Strategy Testing",
      "strategyTestingDesc": "Test different risk settings and see how they perform over time",
      "realMarketData": "Real Market Data",
      "realMarketDataDesc": "Uses live market prices and real ML forecasts for authentic experience",
    },
    "gettingStarted": {
      "title": "Ready to Get Started?",
      "description": "Toggle the \"Trading Mode\" switch to \"Paper\" and start practicing with virtual funds today!",
      "buildConfidenceFooter": "💪 Build confidence with paper trading before risking real money!",
    },
    "perfectForBeginners": "💡 Perfect for beginners and experienced traders who want to test new strategies!",
    "availableToAllTitle": "Available to All:",
    "availableToAllDesc": "Paper trading is available to all users regardless of tier level.",
    "gotItThanks": "Got it, thanks!",
  },
  "paperTradingAnalytics": {
    "title": "Paper Trading Analytics",
    "refresh": "Refresh",
    "periods": {
      "7d": "7D",
      "30d": "30D",
      "90d": "90D",
    },
    "metrics": {
      "totalPnl": "Total P&L",
      "winRate": "Win Rate",
      "totalTrades": "Total Trades",
      "profitFactor": "Profit Factor",
    },
    "tradeBreakdown": {
      "title": "Trade Breakdown",
      "winningTrades": "Winning Trades",
      "losingTrades": "Losing Trades",
    },
    "averagePerformance": {
      "title": "Average Performance",
      "averageWin": "Average Win",
      "averageLoss": "Average Loss",
      "riskRewardRatio": "Risk/Reward Ratio",
    },
    "balanceHistory": {
      "title": "Balance History",
      "chartVisualization": "Balance chart visualization",
      "current": "Current",
    },
    "errors": {
      "loadFailed": "Failed to load analytics data",
    }
  },
  "paperTradingDashboard": {
    "performance": {
      "title": "Paper Trading Performance",
      "refresh": "Refresh",
      "reset": "Reset",
      "totalReturn": "Total Return",
    },
    "analytics": {
      "thirtyDay": "30-Day Analytics",
    },
    "errors": {
      "loadFailed": "Failed to load paper trading data",
    }
  }
};