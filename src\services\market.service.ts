import { API_BASE_URL } from '../config';

interface ForecastResponse {
  status: string;
  symbol: string;
  timeframe: string;
  current_price: number;
  forecast: number[];
  forecast_dates: string[];
  chart_html: string;
  support_level: number;
  resistance_level: number;
  generated_at: string;
  error?: string;
}

export const marketService = {
  async getForecast(symbol: string, timeframe: string): Promise<ForecastResponse> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/forecast/${symbol}?timeframe=${timeframe}`,
        {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.error);
      }

      return {
        status: data.status || 'success',
        symbol: data.symbol,
        timeframe: data.timeframe,
        current_price: data.current_price,
        forecast: data.forecast || [],
        forecast_dates: data.forecast_dates || [],
        chart_html: data.chart_html || '',
        support_level: data.support_level,
        resistance_level: data.resistance_level,
        generated_at: data.generated_at,
      };
    } catch (error) {
      console.error('Error fetching forecast:', error);
      return {
        status: 'error',
        symbol,
        timeframe,
        current_price: 0,
        forecast: [],
        forecast_dates: [],
        chart_html: '',
        support_level: 0,
        resistance_level: 0,
        generated_at: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Failed to fetch forecast',
      };
    }
  },
};

export default marketService;
