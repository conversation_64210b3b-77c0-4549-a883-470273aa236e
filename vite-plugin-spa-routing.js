// This plugin ensures that all routes are handled by the SPA
// and properly served through index.html
export default function spaRouting() {
  return {
    name: 'vite-plugin-spa-routing',
    configureServer(server) {
      // Return a custom middleware to handle all requests
      return () => {
        server.middlewares.use((req, res, next) => {
          // Skip API requests and static files
          if (req.url.startsWith('/api/') || 
              req.url.includes('.') || 
              req.url === '/vite-client') {
            return next();
          }
          // For all other requests, serve index.html
          req.url = '/';
          server.middlewares.handle(req, res, next);
        });
      };
    }
  };
}
