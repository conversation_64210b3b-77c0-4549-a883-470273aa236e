/**
 * Help & Support Page for DeepTrade
 * 
 * Comprehensive help page with FAQ, legal documents, and support information.
 * Includes privacy policy, terms of service, and risk disclosures.
 */

import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import {
  HelpCircle,
  FileText,
  Mail,
  MessageCircle,
  Rocket,
  AlertTriangle,
  Send,
  User,
  AtSign
} from 'lucide-react';
import { TermsOfServiceModal } from '../components/modals/TermsOfServiceModal';
import { PrivacyPolicyModal } from '../components/modals/PrivacyPolicyModal';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { toastSuccess, toastError } from '../components/ui/use-toast';
import { useMobile } from '../hooks/useResponsiveDesign';

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

interface LegalSection {
  id: string;
  title: string;
  content: string[];
}

const Help: React.FC = () => {
  const { t } = useTranslation();
  const { isMobile } = useMobile();
  const [activeTab, setActiveTab] = useState<'faq' | 'legal' | 'contact'>('faq');
  const [expandedFAQ, setExpandedFAQ] = useState<Set<number>>(new Set());
  const [activeLegalSection, setActiveLegalSection] = useState<string>('privacy');
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);

  const faqItems: FAQItem[] = [
    {
      question: "What is DeepTrade?",
      answer: "DeepTrade is an AI-powered cryptocurrency trading platform that provides automated trading signals and portfolio management tools using advanced machine learning algorithms.",
      category: "General"
    },
    {
      question: "How do trading signals work?",
      answer: "Our AI analyzes market data in real-time to generate buy/sell signals with entry points, stop losses, and take profit levels. You can execute these manually or enable auto-trading.",
      category: "Trading"
    },
    {
      question: "What are the different tier levels?",
      answer: "Tier 1 (Free): Basic signals. Tier 2 ($50/month): Advanced signals + auto-trading. Tier 3 (NFT required): Premium features + priority support.",
      category: "Tiers"
    },
    {
      question: "How do I connect my exchange account?",
      answer: "Go to API Credentials in your dashboard, add your exchange API keys (read-only recommended), and our system will validate the connection.",
      category: "Setup"
    },
    {
      question: "Is my data secure?",
      answer: "Yes, we use industry-standard encryption, never store your private keys, and recommend read-only API access for maximum security.",
      category: "Security"
    },
    {
      question: "How do profit shares work?",
      answer: "For Tier 2+ users, we charge a small percentage of profits generated through our signals. This aligns our interests with your success.",
      category: "Billing"
    },
    {
      question: "Can I cancel my subscription anytime?",
      answer: "Yes, you can downgrade or cancel your subscription at any time. You'll retain access until the end of your current billing period.",
      category: "Billing"
    },
    {
      question: "What exchanges are supported?",
      answer: "We currently support major exchanges including Binance, Coinbase Pro, Kraken, and others. Check our API Credentials page for the full list.",
      category: "Trading"
    }
  ];

  const legalSections: LegalSection[] = [
    {
      id: 'privacy',
      title: 'Privacy Policy',
      content: [
        'Last updated: January 15, 2025',
        '',
        '1. INFORMATION WE COLLECT',
        'We collect information you provide directly to us, such as when you create an account, use our services, or contact us for support. This includes:',
        '• Account information (email, name, password)',
        '• Trading preferences and settings',
        '• API credentials (encrypted and secured)',
        '• Usage data and analytics',
        '',
        '2. HOW WE USE YOUR INFORMATION',
        'We use the information we collect to:',
        '• Provide and maintain our trading services',
        '• Generate personalized trading signals',
        '• Process payments and manage subscriptions',
        '• Communicate with you about our services',
        '• Comply with legal obligations',
        '',
        '3. INFORMATION SHARING',
        'We do not sell, trade, or otherwise transfer your personal information to third parties except:',
        '• With your explicit consent',
        '• To comply with legal requirements',
        '• To protect our rights and safety',
        '',
        '4. DATA SECURITY',
        'We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.',
        '',
        '5. CRYPTOCURRENCY COMPLIANCE',
        'We comply with applicable KYC (Know Your Customer) and AML (Anti-Money Laundering) regulations in jurisdictions where we operate.',
        '',
        '6. CONTACT US',
        'If you have questions about this Privacy Policy, please contact <NAME_EMAIL>'
      ]
    },
    {
      id: 'terms',
      title: 'Terms & Conditions',
      content: [
        'Last updated: January 15, 2025',
        '',
        '1. ACCEPTANCE OF TERMS',
        'By accessing and using DeepTrade, you accept and agree to be bound by the terms and provision of this agreement.',
        '',
        '2. DESCRIPTION OF SERVICE',
        'DeepTrade provides AI-powered cryptocurrency trading signals and automated trading tools. Our service is for informational purposes and does not constitute financial advice.',
        '',
        '3. USER RESPONSIBILITIES',
        'You are responsible for:',
        '• Maintaining the confidentiality of your account',
        '• All activities that occur under your account',
        '• Ensuring compliance with local laws and regulations',
        '• Understanding the risks of cryptocurrency trading',
        '',
        '4. TRADING RISKS',
        'Cryptocurrency trading involves substantial risk of loss. Past performance does not guarantee future results. You should never trade with money you cannot afford to lose.',
        '',
        '5. LIABILITY DISCLAIMER',
        'DeepTrade, its staff, and management are not liable for any trading losses, damages, or expenses incurred through the use of our platform or signals.',
        '',
        '6. SUBSCRIPTION AND BILLING',
        'Subscription fees are charged in advance. Profit sharing fees are calculated based on realized gains from our signals.',
        '',
        '7. TERMINATION',
        'We reserve the right to terminate or suspend your account at any time for violation of these terms.',
        '',
        '8. GOVERNING LAW',
        'These terms are governed by the laws of the jurisdiction in which DeepTrade operates.',
        '',
        '9. CONTACT INFORMATION',
        'For questions regarding these terms, contact <EMAIL>'
      ]
    },
    {
      id: 'risks',
      title: 'Risk Disclosure',
      content: [
        'Last updated: January 15, 2025',
        '',
        'By using DeepTrade, you acknowledge and agree to the following:',
        '',
        '1. HIGH RISK OF LOSS',
        'Cryptocurrency trading involves a substantial risk of loss and may not be suitable for all investors. You may lose part or all of your investment.',
        '',
        '2. MARKET VOLATILITY',
        'Crypto markets are highly volatile and may experience sudden and unpredictable changes.',
        '',
        '3. NO GUARANTEES',
        'Past results or performance of signals do not guarantee future outcomes. DeepTrade provides tools, not assurances.',
        '',
        '4. AUTOMATED TRADING RISKS',
        'Automated trading systems can:',
        '• Fail due to technical errors or bugs.',
        '• Execute trades based on inaccurate data or delayed signals.',
        '• Amplify losses during volatile conditions.',
        '',
        '5. API AND EXCHANGE RISKS',
        'API connections may break, become rate-limited, or fail due to:',
        '• Exchange maintenance/downtime.',
        '• Network latency.',
        '• Third-party service outages.',
        '',
        '6. REGULATORY UNCERTAINTY',
        'Crypto regulation is evolving. Laws may change at any time, affecting availability or legality of services.',
        '',
        '7. PERSONAL RESPONSIBILITY',
        'You are solely responsible for all trading actions taken using DeepTrade. The platform provides tools—not financial advice.',
        '',
        '8. NO LIABILITY',
        'DeepTrade and its operators accept no liability for financial losses, technical issues, or regulatory exposure.',
        '',
        '9. ACKNOWLEDGMENT',
        'By continuing to use DeepTrade, you confirm that you understand and accept these risks fully.'
      ]
    }
  ];

  const toggleFAQ = (index: number) => {
    const newExpanded = new Set(expandedFAQ);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedFAQ(newExpanded);
  };

  const handleContactFormChange = (field: string, value: string) => {
    setContactForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleContactFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!contactForm.name || !contactForm.email || !contactForm.subject || !contactForm.message) {
      toastError({
        title: 'Missing Information',
        description: 'Please fill in all fields before submitting.',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/support/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: contactForm.name,
          email: contactForm.email,
          subject: contactForm.subject,
          message: contactForm.message,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      toastSuccess({
        title: 'Message Sent!',
        description: 'Thank you for contacting us. We\'ll get back to you within 24-48 hours.',
      });

      // Reset form
      setContactForm({
        name: '',
        email: '',
        subject: '',
        message: ''
      });
    } catch (error) {
      console.error('Error sending contact form:', error);
      toastError({
        title: 'Error',
        description: 'Failed to send message. Please try again later.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const categories = Array.from(new Set(faqItems.map(item => item.category)));

  const tabs = [
    { id: 'faq', label: t('help.faq'), icon: HelpCircle },
    { id: 'legal', label: t('help.legal'), icon: FileText },
    { id: 'contact', label: t('help.contact'), icon: Mail },
  ];

  return (
    <div className={`${isMobile ? 'p-3 space-y-4' : 'p-6 space-y-6'} max-w-full overflow-x-hidden`}>
      {/* Header */}
      <div className="text-center mb-6 sm:mb-8">
        <h1 className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-foreground mb-3 sm:mb-4 px-2`}>
          {t('help.title')}
        </h1>
        <p className={`${isMobile ? 'text-base px-4' : 'text-lg'} text-muted-foreground max-w-2xl mx-auto leading-relaxed`}>
          Find answers to common questions, review our legal policies, and get in touch with our support team.
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex justify-center mb-6 sm:mb-8 px-2">
        <div className={`flex bg-muted rounded-lg p-1 ${isMobile ? 'w-full max-w-sm' : ''}`}>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center justify-center ${isMobile ? 'flex-1 space-x-1 px-2 py-2.5' : 'space-x-2 px-6 py-3'} text-sm font-medium rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              <tab.icon className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />
              <span className={`${isMobile ? 'text-xs' : ''}`}>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div>
        {activeTab === 'faq' && (
          <div className={`${isMobile ? 'space-y-4' : 'space-y-8'}`}>
            {categories.map((category) => (
              <div key={category} className="bg-card rounded-xl shadow-sm border border-border overflow-hidden">
                <div className={`${isMobile ? 'p-3' : 'p-4 sm:p-6'} border-b border-border`}>
                  <h2 className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold text-foreground`}>
                    {category}
                  </h2>
                </div>
                <div className="divide-y divide-border">
                  {faqItems
                    .filter(item => item.category === category)
                    .map((item) => {
                      const globalIndex = faqItems.indexOf(item);
                      const isExpanded = expandedFAQ.has(globalIndex);

                      return (
                        <div key={globalIndex} className={`${isMobile ? 'p-3' : 'p-4 sm:p-6'}`}>
                          <button
                            onClick={() => toggleFAQ(globalIndex)}
                            className="w-full flex items-center justify-between text-left hover:bg-muted/50 rounded-lg p-2 -m-2 transition-colors"
                          >
                            <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-medium text-foreground pr-4 flex-1 text-left`}>
                              {item.question}
                            </h3>
                            <svg
                              className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} text-muted-foreground transition-transform flex-shrink-0 ${
                                isExpanded ? 'rotate-180' : ''
                              }`}
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </button>
                          {isExpanded && (
                            <div className={`mt-3 sm:mt-4 text-muted-foreground leading-relaxed ${isMobile ? 'text-sm' : ''}`}>
                              {item.answer}
                            </div>
                          )}
                        </div>
                      );
                    })}
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'legal' && (
          <div className={`${isMobile ? 'space-y-4' : 'grid grid-cols-1 lg:grid-cols-4 gap-8'}`}>
            {/* Legal Navigation */}
            <div className={`${isMobile ? '' : 'lg:col-span-1'}`}>
              <div className={`bg-card rounded-xl shadow-sm border border-border ${isMobile ? 'p-3' : 'p-4 sm:p-6'} ${isMobile ? '' : 'sticky top-8'}`}>
                <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold text-foreground mb-3 sm:mb-4`}>
                  Legal Documents
                </h3>
                <nav className={`${isMobile ? 'flex flex-wrap gap-2' : 'space-y-2'}`}>
                  {legalSections.map((section) => (
                    <button
                      key={section.id}
                      onClick={() => setActiveLegalSection(section.id)}
                      className={`${isMobile ? 'flex-1 min-w-0 text-center px-2 py-2 text-xs' : 'w-full text-left px-3 py-2'} rounded-lg transition-colors ${
                        activeLegalSection === section.id
                          ? 'bg-primary/10 text-primary'
                          : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                      }`}
                    >
                      {section.title}
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Legal Content */}
            <div className={`${isMobile ? '' : 'lg:col-span-3'}`}>
              <div className={`bg-card rounded-xl shadow-sm border border-border ${isMobile ? 'p-3' : 'p-4 sm:p-6 lg:p-8'} overflow-hidden`}>
                {activeLegalSection === 'privacy' && (
                  <div>
                    <h2 className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold text-foreground mb-4 sm:mb-6`}>
                      Privacy Policy
                    </h2>
                    <div className={`${isMobile ? 'space-y-4' : 'space-y-6'}`}>
                      <p className={`text-muted-foreground leading-relaxed ${isMobile ? 'text-sm' : ''}`}>
                        Our Privacy Policy outlines how we collect, use, and protect your personal information when you use DeepTrade's services. We are committed to maintaining the privacy and security of your data.
                      </p>
                      <div className={`bg-muted/50 rounded-lg ${isMobile ? 'p-4' : 'p-6'} border border-border`}>
                        <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold text-foreground mb-3`}>
                          Key Privacy Highlights:
                        </h3>
                        <ul className={`space-y-2 text-muted-foreground ${isMobile ? 'text-sm' : ''}`}>
                          <li>• We collect only necessary information to provide our services</li>
                          <li>• Your trading data is encrypted and securely stored</li>
                          <li>• We never sell your personal information to third parties</li>
                          <li>• You have control over your data and privacy settings</li>
                        </ul>
                      </div>
                      <div className="flex flex-col gap-3 sm:gap-4">
                        <button
                          onClick={() => setShowPrivacyModal(true)}
                          className={`bg-primary text-primary-foreground hover:bg-primary/90 ${isMobile ? 'px-4 py-2.5 text-sm' : 'px-6 py-3'} rounded-lg font-medium transition-colors w-full sm:w-auto`}
                        >
                          View Full Privacy Policy
                        </button>
                        <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground text-center sm:text-left`}>
                          Always displayed in English for legal clarity
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {activeLegalSection === 'terms' && (
                  <div>
                    <h2 className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold text-foreground mb-4 sm:mb-6`}>
                      Terms of Service
                    </h2>
                    <div className={`${isMobile ? 'space-y-4' : 'space-y-6'}`}>
                      <p className={`text-muted-foreground leading-relaxed ${isMobile ? 'text-sm' : ''}`}>
                        Our Terms of Service govern your use of DeepTrade's platform and services. By using our platform, you agree to these terms and conditions.
                      </p>
                      <div className={`bg-muted/50 rounded-lg ${isMobile ? 'p-4' : 'p-6'} border border-border`}>
                        <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold text-foreground mb-3`}>
                          Key Terms Highlights:
                        </h3>
                        <ul className={`space-y-2 text-muted-foreground ${isMobile ? 'text-sm' : ''}`}>
                          <li>• Platform usage rights and restrictions</li>
                          <li>• User responsibilities and obligations</li>
                          <li>• Service availability and limitations</li>
                          <li>• Account termination and suspension policies</li>
                        </ul>
                      </div>
                      <div className="flex flex-col gap-3 sm:gap-4">
                        <button
                          onClick={() => setShowTermsModal(true)}
                          className={`bg-primary text-primary-foreground hover:bg-primary/90 ${isMobile ? 'px-4 py-2.5 text-sm' : 'px-6 py-3'} rounded-lg font-medium transition-colors w-full sm:w-auto`}
                        >
                          View Full Terms of Service
                        </button>
                        <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground text-center sm:text-left`}>
                          Always displayed in English for legal clarity
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {activeLegalSection === 'risks' && (
                  <div>
                    <h2 className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold text-foreground mb-4 sm:mb-6`}>
                      Risk Disclosure
                    </h2>
                    <div className={`prose prose-neutral dark:prose-invert max-w-none ${isMobile ? 'prose-sm' : ''}`}>
                      {legalSections
                        .filter(section => section.id === 'risks')
                        .map((section) => (
                          section.content.map((paragraph, index) => (
                            <p key={index} className={`${
                              paragraph === '' ? 'mb-3 sm:mb-4' :
                              paragraph.includes('CRYPTOCURRENCY TRADING RISKS') ? `text-destructive font-bold ${isMobile ? 'text-base' : 'text-lg'} mb-3 sm:mb-4` :
                              paragraph.match(/^\d+\./) ? `font-semibold ${isMobile ? 'text-base' : 'text-lg'} mt-4 sm:mt-6 mb-2 text-foreground` :
                              paragraph.startsWith('•') ? `ml-3 sm:ml-4 mb-1 text-muted-foreground ${isMobile ? 'text-sm' : ''}` :
                              paragraph.startsWith('Last updated:') ? `${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground mb-4 sm:mb-6` :
                              `mb-2 sm:mb-3 text-muted-foreground leading-relaxed ${isMobile ? 'text-sm' : ''}`
                            }`}>
                              {paragraph}
                            </p>
                          ))
                        ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'contact' && (
          <div className={`${isMobile ? '' : 'max-w-4xl mx-auto'}`}>
            <div className={`${isMobile ? 'space-y-6' : 'grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8'}`}>
              {/* Contact Form */}
              <div className={`bg-card rounded-xl shadow-sm border border-border ${isMobile ? 'p-3' : 'p-4 sm:p-6 lg:p-8'} overflow-hidden`}>
                <h2 className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold text-foreground mb-4 sm:mb-6`}>
                  Contact Support
                </h2>
                <form onSubmit={handleContactFormSubmit} className={`${isMobile ? 'space-y-3' : 'space-y-4'}`}>
                  <div className={`${isMobile ? 'space-y-3' : 'grid grid-cols-1 sm:grid-cols-2 gap-4'}`}>
                    <div>
                      <Label htmlFor="contact-name" className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-foreground`}>
                        Name
                      </Label>
                      <div className="relative mt-1">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                        <Input
                          id="contact-name"
                          type="text"
                          value={contactForm.name}
                          onChange={(e) => handleContactFormChange('name', e.target.value)}
                          placeholder="Your full name"
                          className={`pl-10 ${isMobile ? 'text-sm' : ''}`}
                          required
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="contact-email" className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-foreground`}>
                        Email
                      </Label>
                      <div className="relative mt-1">
                        <AtSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                        <Input
                          id="contact-email"
                          type="email"
                          value={contactForm.email}
                          onChange={(e) => handleContactFormChange('email', e.target.value)}
                          placeholder="<EMAIL>"
                          className={`pl-10 ${isMobile ? 'text-sm' : ''}`}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="contact-subject" className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-foreground`}>
                      Subject
                    </Label>
                    <Input
                      id="contact-subject"
                      type="text"
                      value={contactForm.subject}
                      onChange={(e) => handleContactFormChange('subject', e.target.value)}
                      placeholder="Brief description of your issue"
                      className={`mt-1 ${isMobile ? 'text-sm' : ''}`}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="contact-message" className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-foreground`}>
                      Message
                    </Label>
                    <textarea
                      id="contact-message"
                      value={contactForm.message}
                      onChange={(e) => handleContactFormChange('message', e.target.value)}
                      placeholder="Please describe your issue in detail..."
                      rows={isMobile ? 3 : 4}
                      className={`mt-1 w-full px-3 py-2 border border-input bg-background rounded-md ${isMobile ? 'text-sm' : 'text-sm'} placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-none`}
                      required
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className={`w-full flex items-center justify-center space-x-2 ${isMobile ? 'py-2.5 text-sm' : ''}`}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white" />
                        <span>Sending...</span>
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4" />
                        <span>Send Message</span>
                      </>
                    )}
                  </Button>
                </form>
              </div>

              {/* Support Guidelines */}
              <div className={`bg-card rounded-xl shadow-sm border border-border ${isMobile ? 'p-3' : 'p-4 sm:p-6 lg:p-8'} overflow-hidden`}>
                <h2 className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold text-foreground mb-4 sm:mb-6`}>
                  Support Guidelines
                </h2>
                <div className={`${isMobile ? 'space-y-3' : 'space-y-4'}`}>
                  <div>
                    <h3 className={`font-semibold text-foreground mb-2 ${isMobile ? 'text-base' : ''}`}>
                      Before Contacting Support
                    </h3>
                    <ul className={`text-muted-foreground space-y-1 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      <li>• Check this FAQ section first</li>
                      <li>• Review your account settings</li>
                      <li>• Verify your API credentials</li>
                      <li>• Check our status page for known issues</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className={`font-semibold text-foreground mb-2 ${isMobile ? 'text-base' : ''}`}>
                      When Contacting Support
                    </h3>
                    <ul className={`text-muted-foreground space-y-1 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      <li>• Include your account email</li>
                      <li>• Describe the issue in detail</li>
                      <li>• Provide screenshots if applicable</li>
                      <li>• Include error messages</li>
                    </ul>
                  </div>

                  <div className={`bg-yellow-500/10 border border-yellow-500/20 rounded-lg ${isMobile ? 'p-3' : 'p-4'}`}>
                    <h4 className={`font-semibold text-yellow-600 dark:text-yellow-400 mb-2 flex items-center space-x-2 ${isMobile ? 'text-sm' : ''}`}>
                      <AlertTriangle className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} />
                      <span>Important Notice</span>
                    </h4>
                    <p className={`text-yellow-700 dark:text-yellow-300 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      We will never ask for your private keys, passwords, or API secret keys.
                      Keep this information secure and never share it with anyone.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Terms of Service Modal */}
      <TermsOfServiceModal
        isOpen={showTermsModal}
        onClose={() => setShowTermsModal(false)}
      />

      {/* Privacy Policy Modal */}
      <PrivacyPolicyModal
        isOpen={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
      />
    </div>
  );
};

export default Help;
