/**
 * Internationalization Configuration for DeepTrade
 * 
 * Supports 8 languages with automatic detection and persistent storage.
 * Languages: English, Spanish, Portuguese, Korean, Japanese, German, French, Chinese
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import enCommon from './locales/en/common';
import esCommon from './locales/es/common';
import ptCommon from './locales/pt/common';
import koCommon from './locales/ko/common';
import jaCommon from './locales/ja/common';
import deCommon from './locales/de/common';
import frCommon from './locales/fr/common';
import zhCommon from './locales/zh/common';

// Language configuration with flag icons
export const languages = [
  { code: 'en', name: 'English', flag: 'US', nativeName: 'English' },
  { code: 'es', name: 'Spanish', flag: 'ES', nativeName: 'Español' },
  { code: 'pt', name: 'Portuguese', flag: 'BR', nativeName: 'Português' },
  { code: 'ko', name: 'Korean', flag: 'KR', nativeName: '한국어' },
  { code: 'ja', name: 'Japanese', flag: 'JP', nativeName: '日本語' },
  { code: 'de', name: 'German', flag: 'DE', nativeName: 'Deutsch' },
  { code: 'fr', name: 'French', flag: 'FR', nativeName: 'Français' },
  { code: 'zh', name: 'Chinese', flag: 'CN', nativeName: '中文' },
];

// Translation resources
const resources = {
  en: { common: enCommon },
  es: { common: esCommon },
  pt: { common: ptCommon },
  ko: { common: koCommon },
  ja: { common: jaCommon },
  de: { common: deCommon },
  fr: { common: frCommon },
  zh: { common: zhCommon },
};

// Language detection options - Default to English
const detectionOptions = {
  // Order of detection methods - localStorage first, then fallback to English
  order: ['localStorage'],

  // Cache user language
  caches: ['localStorage'],

  // Key for localStorage
  lookupLocalStorage: 'deeptrade-language',

  // Don't lookup from path, subdomain, etc.
  lookupFromPathIndex: 0,
  lookupFromSubdomainIndex: 0,

  // Exclude certain detection methods
  excludeCacheFor: ['cimode'],

  // Check for supported languages only
  checkWhitelist: true,
};

// Initialize i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    
    // Default language
    fallbackLng: 'en',
    
    // Supported languages
    supportedLngs: languages.map(lang => lang.code),
    
    // Language detection
    detection: detectionOptions,
    
    // Namespace configuration
    defaultNS: 'common',
    ns: ['common'],
    
    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
      formatSeparator: ',',
      format: (value, format, lng) => {
        // Custom formatting functions
        if (format === 'currency') {
          return new Intl.NumberFormat(lng, {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 6,
          }).format(value);
        }
        
        if (format === 'percentage') {
          return new Intl.NumberFormat(lng, {
            style: 'percent',
            minimumFractionDigits: 1,
            maximumFractionDigits: 2,
          }).format(value / 100);
        }
        
        if (format === 'number') {
          return new Intl.NumberFormat(lng).format(value);
        }
        
        if (format === 'date') {
          return new Intl.DateTimeFormat(lng, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
          }).format(new Date(value));
        }
        
        if (format === 'datetime') {
          return new Intl.DateTimeFormat(lng, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          }).format(new Date(value));
        }
        
        return value;
      },
    },
    
    // React options
    react: {
      useSuspense: false, // Disable suspense for SSR compatibility
      bindI18n: 'languageChanged',
      bindI18nStore: '',
      transEmptyNodeValue: '',
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em'],
    },
    
    // Development options
    debug: process.env.NODE_ENV === 'development',
    
    // Missing key handling
    saveMissing: process.env.NODE_ENV === 'development',
    missingKeyHandler: (lng, _ns, key, _fallbackValue) => {
      if (process.env.NODE_ENV === 'development') {
        console.warn(`Missing translation key: ${key} for language: ${lng}`);
      }
    },
    
    // Pluralization
    pluralSeparator: '_',
    contextSeparator: '_',
    
    // Performance
    load: 'languageOnly', // Don't load country-specific variants
    preload: ['en'], // Preload English
    
    // Clean code on production
    cleanCode: true,
    
    // Return objects for complex translations
    returnObjects: true,
  });

// Export language utilities
export const getCurrentLanguage = () => i18n.language;

export const changeLanguage = (languageCode: string) => {
  return i18n.changeLanguage(languageCode);
};

export const getLanguageInfo = (code: string) => {
  return languages.find(lang => lang.code === code);
};

export const isRTL = (languageCode?: string) => {
  const lang = languageCode || getCurrentLanguage();
  // Add RTL languages if needed (Arabic, Hebrew, etc.)
  const rtlLanguages = ['ar', 'he', 'fa'];
  return rtlLanguages.includes(lang);
};

// Development utilities
if (process.env.NODE_ENV === 'development') {
  // Import development utilities
  import('./utils/translation-validator').then(module => {
    console.log('🌍 Translation validation utilities loaded');
  });

  import('./utils/add-language').then(module => {
    console.log('🌍 Language management utilities loaded');
  });
}

// Export i18n instance
export default i18n;
