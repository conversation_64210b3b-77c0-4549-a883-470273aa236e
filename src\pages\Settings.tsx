import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toastSuccess, toastError } from '@/components/ui/use-toast';
import { PasswordStrengthIndicator } from '@/components/ui/PasswordStrengthIndicator';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';
import LanguageSelector from '@/components/ui/LanguageSelector';
import { useTranslation } from '@/hooks/useTranslation';
import { useMobile } from '@/hooks/useResponsiveDesign';
import { Mail, Lock, Shield, Trash2, Eye, EyeOff, AlertCircle, Globe } from 'lucide-react';

export default function Settings() {
  const { user, isAuthenticated } = useAuth();
  const { t } = useTranslation();
  const { isMobile } = useMobile();
  const navigate = useNavigate();

  // Email Update State
  const [newEmail, setNewEmail] = useState('');
  const [emailPassword, setEmailPassword] = useState('');
  const [isEmailLoading, setIsEmailLoading] = useState(false);

  // Password Change State
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);

  // 2FA State
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);
  const [twoFACode, setTwoFACode] = useState('');
  const [is2FALoading, setIs2FALoading] = useState(false);
  const [showVerificationInput, setShowVerificationInput] = useState(false);

  // Account Deletion State
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);

  // 2FA Disable Modal State
  const [showDisable2FAModal, setShowDisable2FAModal] = useState(false);

  // 2FA Disable Email Verification State
  const [showDisable2FAVerification, setShowDisable2FAVerification] = useState(false);
  const [disable2FACode, setDisable2FACode] = useState('');

  // Check authentication
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    
    // Set initial 2FA state from user data
    if (user?.two_fa_enabled) {
      setIs2FAEnabled(true);
    }
  }, [isAuthenticated, navigate, user]);

  // Email Update Handler
  const handleEmailUpdate = async () => {
    if (!newEmail.trim() || !emailPassword.trim()) {
      toastError({
        title: 'Error',
        description: 'Please fill in all fields',
      });
      return;
    }

    setIsEmailLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/users/email', {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify({
          new_email: newEmail,
          current_password: emailPassword,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update email');
      }

      toastSuccess({
        title: 'Verification Email Sent',
        description: `Please check ${newEmail} for verification instructions.`,
      });

      setNewEmail('');
      setEmailPassword('');
      
    } catch (error) {
      console.error('Email update failed:', error);
      toastError({
        title: 'Email Update Failed',
        description: error instanceof Error ? error.message : 'Failed to update email',
      });
    } finally {
      setIsEmailLoading(false);
    }
  };

  // Password Change Handler
  const handlePasswordChange = async () => {
    if (!currentPassword.trim() || !newPassword.trim() || !confirmNewPassword.trim()) {
      toastError({
        title: 'Error',
        description: 'Please fill in all fields',
      });
      return;
    }

    if (newPassword !== confirmNewPassword) {
      toastError({
        title: 'Error',
        description: 'New passwords do not match',
      });
      return;
    }

    setIsPasswordLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/users/password', {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify({
          current_password: currentPassword,
          new_password: newPassword,
          confirm_password: confirmNewPassword,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to change password');
      }

      toastSuccess({
        title: 'Password Changed',
        description: 'Your password has been updated successfully.',
      });

      setCurrentPassword('');
      setNewPassword('');
      setConfirmNewPassword('');
    } catch (error: any) {
      console.error('Password change failed:', error);
      toastError({
        title: 'Password Change Failed',
        description: error.message || 'Failed to change password',
      });
    } finally {
      setIsPasswordLoading(false);
    }
  };

  // 2FA Enable Handler
  const handle2FAEnable = async () => {
    setIs2FALoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/users/2fa/enable', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to enable 2FA');
      }

      toastSuccess({
        title: '2FA Code Sent',
        description: 'Please check your email for the verification code.',
      });

      setShowVerificationInput(true);

    } catch (error: any) {
      console.error('2FA enable failed:', error);
      toastError({
        title: 'Failed to enable 2FA',
        description: error.message || 'Failed to enable 2FA',
      });
    } finally {
      setIs2FALoading(false);
    }
  };

  // 2FA Verify Handler
  const handle2FAVerify = async () => {
    if (!twoFACode.trim()) {
      toastError({
        title: 'Error',
        description: 'Please enter the verification code',
      });
      return;
    }

    setIs2FALoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/users/2fa/verify', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify({
          code: twoFACode,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to verify 2FA code');
      }

      toastSuccess({
        title: '2FA Enabled',
        description: 'Two-factor authentication has been enabled successfully.',
      });

      setIs2FAEnabled(true);
      setShowVerificationInput(false);
      setTwoFACode('');

    } catch (error: any) {
      console.error('2FA verify failed:', error);
      toastError({
        title: 'Failed to verify 2FA',
        description: error.message || 'Failed to verify 2FA code',
      });
    } finally {
      setIs2FALoading(false);
    }
  };

  // 2FA Disable - Send Email Verification
  const handle2FADisableRequest = async () => {
    setIs2FALoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/users/2fa/disable/request', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send disable verification code');
      }

      toastSuccess({
        title: 'Verification Code Sent',
        description: 'Please check your email for the 6-digit verification code to disable 2FA.',
      });

      setShowDisable2FAModal(false);
      setShowDisable2FAVerification(true);

    } catch (error: any) {
      console.error('2FA disable request failed:', error);
      toastError({
        title: 'Failed to send verification code',
        description: error.message || 'Failed to send verification code',
      });
    } finally {
      setIs2FALoading(false);
    }
  };

  // 2FA Disable - Verify Email Code
  const handle2FADisableVerify = async () => {
    if (!disable2FACode.trim() || disable2FACode.length !== 6) {
      toastError({
        title: 'Error',
        description: 'Please enter a valid 6-digit verification code',
      });
      return;
    }

    setIs2FALoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/users/2fa/disable/verify', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify({
          code: disable2FACode,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to disable 2FA');
      }

      toastSuccess({
        title: '2FA Disabled',
        description: 'Two-factor authentication has been disabled successfully.',
      });

      setIs2FAEnabled(false);
      setShowDisable2FAVerification(false);
      setDisable2FACode('');

    } catch (error: any) {
      console.error('2FA disable verification failed:', error);
      toastError({
        title: 'Failed to disable 2FA',
        description: error.message || 'Invalid verification code or failed to disable 2FA',
      });
    } finally {
      setIs2FALoading(false);
    }
  };

  // Account Deletion Handler
  const handleAccountDeletion = async (data: { password?: string; typedConfirmation?: string }) => {
    setIsDeleteLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/users/delete', {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify({
          current_password: data.password,
          confirmation_text: data.typedConfirmation,
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to delete account');
      }

      toastSuccess({
        title: 'Account Deleted',
        description: 'Your account has been successfully deleted.',
      });

      // Logout and redirect
      localStorage.removeItem('access_token');
      navigate('/login');

    } catch (error: any) {
      console.error('Account deletion failed:', error);
      toastError({
        title: 'Deletion Failed',
        description: error.message || 'Failed to delete account',
      });
    } finally {
      setIsDeleteLoading(false);
      setShowDeleteModal(false);
    }
  };

  if (!isAuthenticated || !user) {
    return (
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Please Log In</h1>
          <p className="text-sm text-muted-foreground">
            You need to be logged in to access account settings.
          </p>
        </div>
        <Link to="/login">
          <Button className="w-full">Go to Login</Button>
        </Link>
      </div>
    );
  }

  // Note: Removed mobile redirect to ensure feature parity between desktop and mobile

  return (
    <div className={`container mx-auto ${isMobile ? 'max-w-full px-3 py-4 space-y-4' : 'max-w-4xl p-4 sm:p-6 space-y-6 sm:space-y-8'} overflow-x-hidden`}>
      <div className="text-center">
        <h1 className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold tracking-tight`}>{t('settings.title')}</h1>
        <p className={`text-muted-foreground mt-2 ${isMobile ? 'text-sm px-2' : ''}`}>
          {t('settings.profile.description')}
        </p>
      </div>

      {/* Email Update Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 sm:p-6 space-y-4">
        <div className="flex items-center space-x-2">
          <Mail className="w-5 h-5 text-blue-600" />
          <h2 className="text-xl font-semibold">{t('settings.profile.email')}</h2>
        </div>
        <p className="text-sm text-muted-foreground">
          {t('settings.profile.description')}
        </p>
        <div className="space-y-4">
          <div>
            <Label htmlFor="current-email">{t('settings.profile.email')}</Label>
            <Input
              id="current-email"
              type="email"
              value={user?.email || ''}
              disabled
              className="bg-gray-50 dark:bg-gray-700"
            />
          </div>
          <div>
            <Label htmlFor="new-email" required>{t('settings.profile.email')}</Label>
            <Input
              id="new-email"
              type="email"
              value={newEmail}
              onChange={(e) => setNewEmail(e.target.value)}
              placeholder="Enter your new email address"
              disabled={isEmailLoading}
            />
          </div>
          <div>
            <Label htmlFor="email-password" required>{t('settings.security.currentPassword')}</Label>
            <Input
              id="email-password"
              type="password"
              value={emailPassword}
              onChange={(e) => setEmailPassword(e.target.value)}
              placeholder="Enter your current password"
              disabled={isEmailLoading}
            />
          </div>
          <Button
            onClick={handleEmailUpdate}
            disabled={isEmailLoading || !newEmail.trim() || !emailPassword.trim()}
            className="w-full sm:w-auto"
          >
            {isEmailLoading ? 'Sending...' : 'Update Email'}
          </Button>
        </div>
      </div>

      {/* Password Change Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 sm:p-6 space-y-4">
        <div className="flex items-center space-x-2">
          <Lock className="w-5 h-5 text-green-600" />
          <h2 className="text-xl font-semibold">{t('settings.security.changePassword')}</h2>
        </div>
        <p className="text-sm text-muted-foreground">
          {t('settings.security.description')}
        </p>
        <div className="space-y-4">
          <div>
            <Label htmlFor="current-password" required>{t('settings.security.currentPassword')}</Label>
            <div className="relative">
              <Input
                id="current-password"
                type={showCurrentPassword ? "text" : "password"}
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                placeholder="Enter your current password"
                disabled={isPasswordLoading}
              />
              <button
                type="button"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                {showCurrentPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>
          <div>
            <Label htmlFor="new-password" required>{t('settings.security.newPassword')}</Label>
            <div className="relative">
              <Input
                id="new-password"
                type={showNewPassword ? "text" : "password"}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="Enter your new password"
                disabled={isPasswordLoading}
              />
              <button
                type="button"
                onClick={() => setShowNewPassword(!showNewPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                {showNewPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>
          <div>
            <Label htmlFor="confirm-new-password" required>{t('settings.security.confirmPassword')}</Label>
            <Input
              id="confirm-new-password"
              type="password"
              value={confirmNewPassword}
              onChange={(e) => setConfirmNewPassword(e.target.value)}
              placeholder="Confirm your new password"
              disabled={isPasswordLoading}
            />
          </div>
          {newPassword && (
            <PasswordStrengthIndicator password={newPassword} />
          )}
          <Button
            onClick={handlePasswordChange}
            disabled={isPasswordLoading || !currentPassword.trim() || !newPassword.trim() || !confirmNewPassword.trim()}
            className="w-full sm:w-auto"
          >
            {isPasswordLoading ? 'Changing...' : 'Change Password'}
          </Button>
        </div>
      </div>

      {/* 2FA Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 sm:p-6 space-y-4">
        <div className="flex items-center space-x-2">
          <Shield className="w-5 h-5 text-purple-600" />
          <h2 className="text-xl font-semibold">{t('settings.security.enable2FA')}</h2>
        </div>
        <p className="text-sm text-muted-foreground">
          {t('settings.security.description')}
        </p>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div>
              <h3 className="font-medium">{t('settings.security.enable2FA')}</h3>
              <p className="text-sm text-muted-foreground">
                {is2FAEnabled ? t('settings.security.twoFactorEnabled') : t('settings.security.twoFactorDisabled')}
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${is2FAEnabled ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'}`} />
              <span className="text-sm font-medium">
                {is2FAEnabled ? t('common.active') : t('common.inactive')}
              </span>
              {/* 2FA Toggle Switch */}
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={is2FAEnabled}
                  onChange={(e) => {
                    if (e.target.checked) {
                      // Enable 2FA
                      handle2FAEnable();
                    } else {
                      // Disable 2FA - show confirmation modal
                      setShowDisable2FAModal(true);
                    }
                  }}
                  disabled={is2FALoading || showVerificationInput}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 peer-disabled:opacity-50 peer-disabled:cursor-not-allowed"></div>
              </label>
            </div>
          </div>

          {showVerificationInput && (
            <div className="space-y-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                <h4 className="font-medium text-blue-900 dark:text-blue-100">Verify Your Email</h4>
              </div>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                We've sent a 6-digit verification code to your email. Enter it below to enable 2FA.
              </p>
              <div>
                <Label htmlFor="2fa-code" required>Verification Code</Label>
                <Input
                  id="2fa-code"
                  type="text"
                  value={twoFACode}
                  onChange={(e) => setTwoFACode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  placeholder="Enter 6-digit code"
                  disabled={is2FALoading}
                  maxLength={6}
                />
              </div>
              <div className="flex space-x-2">
                <Button
                  onClick={handle2FAVerify}
                  disabled={is2FALoading || twoFACode.length !== 6}
                  size="sm"
                >
                  {is2FALoading ? 'Verifying...' : 'Verify Code'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowVerificationInput(false);
                    setTwoFACode('');
                  }}
                  disabled={is2FALoading}
                  size="sm"
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          {/* 2FA Disable Confirmation Modal */}
          <ConfirmationModal
            isOpen={showDisable2FAModal}
            onClose={() => setShowDisable2FAModal(false)}
            onConfirm={() => handle2FADisableRequest()}
            title="Disable Two-Factor Authentication"
            description="Are you sure you want to disable 2FA? This will make your account less secure. We'll send a verification code to your email to confirm this action."
            confirmText="Send Verification Code"
            variant="warning"
            requirePassword={false}
          />

          {/* 2FA Disable Email Verification */}
          {showDisable2FAVerification && (
            <div className="space-y-4 p-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                <h4 className="font-medium text-orange-900 dark:text-orange-100">Verify Email to Disable 2FA</h4>
              </div>
              <p className="text-sm text-orange-800 dark:text-orange-200">
                We've sent a 6-digit verification code to your email. Enter it below to disable two-factor authentication.
              </p>
              <div>
                <Label htmlFor="disable-2fa-code" required>Verification Code</Label>
                <Input
                  id="disable-2fa-code"
                  type="text"
                  value={disable2FACode}
                  onChange={(e) => setDisable2FACode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  placeholder="Enter 6-digit code"
                  disabled={is2FALoading}
                  maxLength={6}
                  className="text-center text-lg tracking-widest"
                />
              </div>
              <div className="flex space-x-2">
                <Button
                  onClick={handle2FADisableVerify}
                  disabled={is2FALoading || disable2FACode.length !== 6}
                  size="sm"
                  variant="destructive"
                >
                  {is2FALoading ? 'Disabling...' : 'Disable 2FA'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDisable2FAVerification(false);
                    setDisable2FACode('');
                  }}
                  disabled={is2FALoading}
                  size="sm"
                >
                  Cancel
                </Button>
                <Button
                  variant="ghost"
                  onClick={handle2FADisableRequest}
                  disabled={is2FALoading}
                  size="sm"
                >
                  {is2FALoading ? 'Sending...' : 'Resend Code'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Language & Preferences Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 sm:p-6 space-y-4">
        <div className="flex items-center space-x-2">
          <Globe className="w-5 h-5 text-blue-600" />
          <h2 className="text-xl font-semibold">{t('settings.language.title')}</h2>
        </div>
        <p className="text-sm text-muted-foreground">
          {t('settings.language.description')}
        </p>

        <div className="space-y-4">
          <div>
            <Label htmlFor="language-selector" className="text-sm font-medium">
              {t('settings.language.selectLanguage')}
            </Label>
            <p className="text-xs text-muted-foreground mb-2">
              {t('settings.language.languageDescription')}
            </p>
            <LanguageSelector variant="settings" />
          </div>
        </div>
      </div>

      {/* Danger Zone */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border-2 border-red-200 dark:border-red-800 p-4 sm:p-6 space-y-4">
        <div className="flex items-center space-x-2">
          <Trash2 className="w-5 h-5 text-red-600" />
          <h2 className="text-xl font-semibold text-red-900 dark:text-red-100">{t('settings.account.title')}</h2>
        </div>
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <h3 className="font-medium text-red-900 dark:text-red-100 mb-2">{t('settings.account.deleteAccount')}</h3>
          <p className="text-sm text-red-800 dark:text-red-200 mb-4">
            {t('settings.account.deleteAccountWarning')}
          </p>
          <ul className="text-sm text-red-700 dark:text-red-300 space-y-1 mb-4">
            <li>• All your data will be permanently deleted</li>
            <li>• Your trading history will be removed</li>
            <li>• Active subscriptions will be cancelled</li>
            <li>• This action cannot be undone</li>
          </ul>
          <Button
            variant="destructive"
            onClick={() => setShowDeleteModal(true)}
            disabled={isDeleteLoading}
          >
            {isDeleteLoading ? 'Deleting...' : 'Delete Account'}
          </Button>
        </div>
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleAccountDeletion}
        title="Delete Account"
        description="This action cannot be undone. This will permanently delete your account and remove all associated data."
        confirmText="Delete Account"
        variant="danger"
        requirePassword={true}
        requireTypedConfirmation={true}
        typedConfirmationText="DELETE"
        isLoading={isDeleteLoading}
      >
        <div className="space-y-2">
          <h4 className="font-medium text-red-900">This will permanently:</h4>
          <ul className="text-sm text-red-800 space-y-1">
            <li>• Delete your account and profile</li>
            <li>• Remove all trading data and history</li>
            <li>• Cancel any active subscriptions</li>
            <li>• Remove all API credentials</li>
          </ul>
        </div>
      </ConfirmationModal>
    </div>
  );
}
