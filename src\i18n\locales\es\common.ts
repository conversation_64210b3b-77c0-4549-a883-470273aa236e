export default {
  "app": {
    "name": "DeepTrade",
    "tagline": "Trading de Criptomonedas con IA",
    "description": "Señales de trading avanzadas y gestión automatizada de portafolio",
  },
  "navigation": {
    "dashboard": "Panel",
    "trading": "Trading",
    "signals": "Señales",
    "apiCredentials": "Credenciales API",
    "autoTrading": "Trading Automático",
    "tierManagement": "Gestión de Niveles",
    "referrals": "Referencias",
    "accessSecurity": "Acceso y Seguridad",
    "settings": "Configuración",
    "help": "Ayuda y Soporte",
    "login": "Iniciar Sesión",
    "register": "Registrarse",
    "logout": "Cerrar Sesión",
    "home": "Inicio",
  },
  "auth": {
    "login": {
      "title": "Bienvenido de Nuevo",
      "subtitle": "Ingresa tu email y contraseña para iniciar sesión",
      "email": "Email",
      "password": "Contraseña",
      "emailPlaceholder": "<EMAIL>",
      "passwordPlaceholder": "••••••••",
      "rememberMe": "Recordarme",
      "forgotPassword": "¿Olvidaste tu contraseña?",
      "signIn": "Iniciar Sesión",
      "noAccount": "¿No tienes una cuenta?",
      "signUp": "Registrarse",
      "googleSignIn": "Continuar con Google",
      "orContinueWith": "O continuar con",
      "fillAllFields": "Por favor completa todos los campos",
      "loginSuccess": "¡Inicio de sesión exitoso!",
      "loginFailed": "Error al iniciar sesión",
      "loginFailedDescription": "No se pudo iniciar sesión",
    },
    "register": {
      "title": "Crear Cuenta",
      "subtitle": "Ingresa tu información para crear una cuenta",
      "firstName": "Nombre",
      "lastName": "Apellido",
      "email": "Correo Electrónico",
      "password": "Contraseña",
      "confirmPassword": "Confirmar Contraseña",
      "agreeTerms": "Al hacer clic en continuar, aceptas nuestros",
      "createAccount": "Crear cuenta",
      "hasAccount": "¿Ya tienes una cuenta?",
      "signIn": "Iniciar sesión",
      "orContinueWith": "O continuar con",
      "googleSignUp": "Registrarse con Google",
      "termsOfService": "Términos de Servicio",
      "privacyPolicy": "Política de Privacidad",
    },
    "passwordRequirements": {
      "title": "Requisitos de Contraseña",
      "length": "Al menos 8 caracteres",
      "uppercase": "Una letra mayúscula",
      "lowercase": "Una letra minúscula",
      "number": "Un número",
      "special": "Un carácter especial",
    },
    "errors": {
      "invalidCredentials": "Email o contraseña inválidos",
      "emailRequired": "Email es requerido",
      "passwordRequired": "Contraseña es requerida",
      "passwordTooShort": "La contraseña debe tener al menos 8 caracteres",
      "passwordsNotMatch": "Las contraseñas no coinciden",
      "emailInvalid": "Por favor ingresa una dirección de email válida",
      "termsRequired": "Debes aceptar los términos y condiciones",
    }
  },
  "dashboard": {
    "title": "Panel",
    "welcome": "¡Bienvenido de vuelta!",
    "overview": "Resumen",
    "trading": "Trading",
    "tier": "Nivel",
    "balance": "Balance",
    "pnlToday": "P&L Hoy",
    "totalTrades": "Operaciones Totales",
    "winRate": "Tasa de Éxito",
    "activeSignals": "Señales Activas",
    "tradingOverview": "Resumen de Trading",
    "quickActions": "Acciones Rápidas",
    "viewSignals": "Ver Señales",
    "autoTrading": "Trading Automático",
    "autoTradingDescription": "Ejecutar señales de trading automáticamente",
    "currentTier": "Nivel Actual",
    "progressTo": "Progreso al Nivel {{tier},",
    "yourBenefits": "Tus Beneficios",
    "advancedSignals": "Señales de Trading Avanzadas",
    "autoTradingFeatures": "Funciones de Trading Automático",
    "prioritySupport": "Soporte Prioritario",
    "monthlyRate": "Tasa Mensual",
    "profitShare30": "30% participación en ganancias",
    "profitShare20": "20% participación en ganancias",
    "profitShare10": "10% participación en ganancias",
    "verifyingNFT": "Verificando propiedad del NFT...",
    "debt": "Deuda",
    "btcPrice": {
      "loading": "BTC/USDT: Cargando...",
      "unavailable": "BTC/USDT: --",
      "connecting": "Conectando...",
      "live": "En Vivo",
      "error": "Error ({{count}})",
      "24h": "24h",
    },
    "forecast": {
      "title": "Pronóstico de Precios",
      "refresh": "Actualizar",
      "errorLoading": "Error cargando gráfico",
      "retry": "Reintentar",
      "noData": "No hay datos de gráfico disponibles",
    },
    "riskSettings": {
      "title": "Configuración de Gestión de Riesgo",
      "paperTradingTitle": "Configuración de Gestión de Riesgo - Trading de Simulación",
      "loading": "Cargando configuración de riesgo...",
      "investmentPercentage": "Porcentaje de Inversión",
      "investmentDescription": "Porcentaje del balance a usar para trading (0% - 10%)",
      "leverage": "Apalancamiento",
      "leverageDescription": "Máx: {{max}}x para Nivel {{tier}} + {{exchange}} {{market},",
      "paperTradingLeverageDesc": "En modo simulación, puedes usar cualquier configuración de apalancamiento para pruebas",
      "exchange": "Exchange: ",
      "configureCredentials": "Configurar en Credenciales API",
      "warningZeroInvestment": "Selecciona porcentaje de inversión > 0% para habilitar trading",
      "paperTradingNote": "Modo trading de simulación - no aplican limitaciones de nivel",
      "saveSettings": "Guardar Configuración",
      "saving": "Guardando...",
      "resetToSafe": "Restablecer a Seguro",
    },
    "activePositions": {
      "title": "Posiciones Activas",
      "tabs": {
        "all": "todas",
        "app": "app",
        "external": "externas",
      },
      "refresh": "Actualizar",
      "errorLoading": "Error cargando posiciones activas",
      "retry": "Reintentar",
      "noPositions": "No hay posiciones activas.",
      "noPositionsDesc": "Tus posiciones de trading activas aparecerán aquí una vez que comiences a operar o habilites el auto-trading.",
      "noAppPositions": "No hay posiciones activas de la app.",
      "noExternalPositions": "No hay posiciones externas activas.",
      "addCredentialsWarning": "⚠️ Agrega credenciales API para ver posiciones externas",
      "externalError": "❌ Error en posiciones externas: {{error},",
      "externalErrorGeneric": "❌ {{error},",
      "diagnostics": "App: {{app}} | Externas: {{external},",
      "fields": {
        "size": "Tamaño",
        "entryPrice": "Precio de Entrada",
        "currentPrice": "Precio Actual",
        "margin": "Margen",
        "liquidationPrice": "Precio Liq.",
        "marginRate": "Tasa de Margen",
        "takeProfit": "TP",
        "stopLoss": "SL",
        "roi": "ROI",
        "none": "Ninguno",
        "na": "N/A",
      },
      "positionTypes": {
        "appPositions": "Posiciones de la App",
        "externalPositions": "Posiciones Externas",
      }
    },
    "profitShare": {
      "title": "Seguimiento de Participación en Ganancias",
      "loading": "Cargando datos de participación en ganancias...",
      "noData": "No hay datos de participación en ganancias disponibles. Comienza a hacer trading para rastrear tus ganancias.",
      "balanceOverview": "Resumen de Balance",
      "initialBalance": "Balance Inicial:",
      "currentBalance": "Balance Actual:",
      "netDeposits": "Depósitos Netos:",
      "profitAnalysis": "Análisis de Ganancias",
      "tradingProfit": "Ganancia de Trading:",
      "trueProfit": "Ganancia Real:",
      "profitPercentage": "% de Ganancia:",
      "profitShareInfo": "Participación en Ganancias",
      "tierRate": "Tasa Nivel {{tier}}:",
      "amountOwed": "Cantidad Adeudada:",
      "status": "Estado:",
      "profitable": "La cuenta está en ganancias - La participación en ganancias se aplica a las ganancias por encima del balance inicial",
      "notProfitable": "La cuenta aún no es rentable - No hay participación en ganancias hasta que el balance exceda el inicial + depósitos",
      "paymentRequired": "Pago Requerido",
      "paymentDescription": "Tienes ${{amount}} en tarifas de participación en ganancias por pagar.",
      "payNow": "Pagar Ahora",
    },
    "tradingHistory": {
      "title": "Historial de Trading (Solo Operaciones de la App)",
      "description": "Mostrando operaciones iniciadas por la plataforma DeepTrade",
      "loading": "Cargando...",
      "headers": {
        "date": "Fecha",
        "symbol": "Símbolo",
        "side": "Lado",
        "size": "Tamaño",
        "entry": "Entrada",
        "exit": "Salida",
        "pnl": "P&L",
        "status": "Estado",
      },
      "noHistory": "No se encontró historial de trading. Comienza a hacer trading para ver tu historial aquí.",
      "pagination": "Página {{page}} de {{pages}} ({{total}} operaciones totales)",
      "previous": "Anterior",
      "next": "Siguiente",
    },
    "blockedUser": {
      "title": "Trading Bloqueado:",
      "message": "Participación en ganancias impaga para tu nivel ({{tier}}). Adeudado: ${{owed}}. Por favor liquida tu balance para continuar.",
    }
  },
  "trading": {
    "title": "Trading",
    "signals": "Señales",
    "positions": "Posiciones",
    "history": "Historial",
    "autoTrading": "Trading Automático",
    "autoTradingActive": "Trading Automático Activo",
    "autoTradingDisabled": "Trading Automático Deshabilitado",
    "autoTradingActiveDesc": "Las señales se ejecutarán automáticamente",
    "autoTradingDisabledDesc": "Habilita para ejecutar señales automáticamente",
    "noActiveSignals": "Sin Señales Activas",
    "waitingForSignals": "Esperando nuevas oportunidades de trading...",
    "enableAutoTrading": "Habilita el trading automático para recibir señales",
    "refreshSignals": "Actualizar Señales",
    "noOpenPositions": "Sin Posiciones Abiertas",
    "positionsDesc": "Tus posiciones de trading activas aparecerán aquí",
    "noTradingHistory": "Sin Historial de Trading",
    "historyDesc": "Tus operaciones completadas aparecerán aquí",
    "executeTradeButton": "Ejecutar Operación",
    "viewDetailsButton": "Ver Detalles",
    "entry": "Entrada",
    "stopLoss": "Stop Loss",
    "takeProfit": "Take Profit",
    "confidence": "{{value}}% confianza",
    "strategy": "Estrategia",
    "firstTp": "Primer TP",
    "secondTp": "Segundo TP",
    "autoMoveStopLoss": "Mover SL automáticamente al punto de equilibrio después del primer TP",
    "tradingMode": "Modo de Trading",
    "paperMode": "Simulación",
    "liveMode": "Real",
    "paperTradingActive": "Trading de Simulación Activo",
    "liveTradingActive": "Trading Real Activo",
    "paperTradingDesc": "Practica trading con fondos virtuales",
    "liveTradingDesc": "Opera con dinero real",
    "virtualBalance": "Balance Virtual",
    "paperTradingHelp": "Ayuda de Trading de Simulación",
    "resetAccount": "Reiniciar Cuenta",
    "paperTradingGuide": "Guía de Trading de Simulación",
    "paperModeTitle": "Modo Simulación",
    "noProfitSharePaper": "Sin participación de ganancias en modo simulación",
    "paperTradingPositions": "Posiciones de Trading de Simulación",
    "paperTradingHistory": "Historial de Trading de Simulación",
    "simulatedTradingHistory": "Historial de trading simulado",
    "noPaperPositions": "Sin posiciones de trading de simulación",
    "enableAutoTradingPaper": "El trading de simulación generará automáticamente posiciones simuladas basadas en señales del mercado",
    "paperTradingMode": "Modo Trading de Simulación",
    "autoTradingNotAvailable": "Trading Automático No Disponible",
    "autoTradingDisabledPaper": "El trading automático controla el trading en vivo. El trading de simulación genera señales automáticamente.",
    "riskManagement": "Gestión de Riesgo",
    "riskSettings": "Configuración de Riesgo",
    "investmentPercentage": "Porcentaje de Inversión",
    "leverage": "Apalancamiento",
    "maxLeverage": "Apalancamiento Máximo",
    "currentTier": "Nivel Actual",
    "exchange": "Exchange",
    "accountType": "Tipo de Cuenta",
    "marketType": "Tipo de Mercado",
    "saveSettings": "Guardar Configuración",
    "resetToSafe": "Restablecer a Seguro",
    "saving": "Guardando...",
    "riskSettingsSaved": "Configuración de riesgo guardada exitosamente",
    "failedToSaveRiskSettings": "Error al guardar configuración de riesgo",
    "position": "posición",
    "internal": "Interno",
    "external": "Externo",
    "marketClose": "Cerrar MKT",
    "reverse": "Revertir",
    "editTpSl": "TP/SL",
    "refreshActivePositions": "Actualizar Posiciones Activas",
    "youHaveNoPosition": "No tienes posiciones.",
    "noActiveAppPositions": "No se encontraron posiciones activas de la app.",
    "noActiveExternalPositions": "No se encontraron posiciones externas activas.",
    "yourActivePositionsWillAppear": "Tus posiciones de trading activas aparecerán aquí.",
    "previous": "Anterior",
    "next": "Siguiente",
    "learnMore": "Aprender Más",
    "paperTradingLabel": "Trading de Simulación",
    "recentPaperTrades": "Operaciones de Simulación Recientes",
    "noPaperTrades": "Aún no hay operaciones de simulación",
    "paperTradingAutoGenerate": "El trading de simulación generará automáticamente operaciones simuladas basadas en señales del mercado",
    "activePositionsTitle": "Posiciones Activas",
    "youHaveNoPositions": "No tienes posiciones.",
    "yourActivePositionsWillAppearHere": "Tus posiciones de trading activas aparecerán aquí una vez que comiences a operar o habilites el trading automático.",
    "max": "Máx",
  },
  "common": {
    "loading": "Cargando...",
    "error": "Error",
    "success": "Éxito",
    "warning": "Advertencia",
    "confirm": "Confirmar",
    "cancel": "Cancelar",
    "save": "Guardar",
    "edit": "Editar",
    "delete": "Eliminar",
    "close": "Cerrar",
    "back": "Atrás",
    "next": "Siguiente",
    "yes": "Sí",
    "no": "No",
    "active": "Activo",
    "inactive": "Inactivo",
    "on": "ENCENDIDO",
    "off": "APAGADO",
    "enabled": "habilitado",
    "disabled": "deshabilitado",
    "for": "para",
    "info": "Información",
    "previous": "Anterior",
    "submit": "Submit",
    "reset": "Reset",
    "search": "Search",
    "filter": "Filter",
    "sort": "Sort",
    "refresh": "Refresh",
    "enable": "Habilitar",
    "disable": "Deshabilitar",
    "toggleOn": "ON",
    "toggleOff": "OFF",
    "ok": "OK",
    "apply": "Aplicar",
    "clear": "Limpiar",
    "select": "Seleccionar",
    "upload": "Subir",
    "download": "Descargar",
    "copy": "Copiar",
    "copied": "¡Copiado!",
    "share": "Share",
    "print": "Imprimir",
    "export": "Export",
    "import": "Import",
    "and": "y",  },
  "wallet": {
    "connect": "Conectar Billetera",
    "disconnect": "Desconectar",
    "connected": "Conectado",
    "notConnected": "No Conectado",
    "connecting": "Conectando...",
    "balance": "Balance",
    "address": "Dirección",
    "solanaBlockchain": "Impulsado por Blockchain Solana",
    "solanaTooltip": "Construido en Solana - Blockchain rápida, segura y escalable",
    "wrongNetwork": "Red Incorrecta",
    "switchNetwork": "Cambiar Red",
    "transactionPending": "Transacción Pendiente",
    "transactionConfirmed": "Transacción Confirmada",
    "transactionFailed": "Transacción Fallida",
    "connectionError": "Error de Conexión",
    "connectionFailed": "Error al conectar billetera",
    "connectionRejected": "La conexión de billetera fue rechazada",
    "walletNotFound": "No se encontró billetera Solana. Por favor instala una billetera como Phantom.",
    "disconnected": "Billetera Desconectada",
    "disconnectedSuccess": "Tu billetera ha sido desconectada exitosamente.",
    "disconnectError": "Error de Desconexión",
    "disconnectFailed": "Error al desconectar billetera.",
    "confirmDisconnect": "Confirmar Desconexión de Billetera",
    "confirmDisconnectMessage": "¿Estás seguro de que quieres desconectar tu billetera?",
  },
  "notifications": {
    "autoTradingEnabled": "El trading automático ha sido habilitado",
    "autoTradingDisabled": "El trading automático ha sido deshabilitado",
    "settingsSaved": "Configuraciones guardadas exitosamente",
    "passwordChanged": "Contraseña cambiada exitosamente",
    "profileUpdated": "Perfil actualizado exitosamente",
    "walletConnected": "Billetera conectada exitosamente",
    "walletDisconnected": "Billetera desconectada",
    "signalExecuted": "Señal de trading ejecutada",
    "tradeCompleted": "Operación completada exitosamente",
    "paymentSuccessful": "Pago procesado exitosamente",
    "tierUpgraded": "Nivel actualizado exitosamente",
    "autoTrading": {
      "enabledTitle": "Trading Automático Habilitado",
      "enabledDescription": "El trading automático ha sido habilitado exitosamente. Requisito de balance verificado.",
      "disabledTitle": "Trading Automático Deshabilitado",
      "disabledDescription": "El trading automático ha sido deshabilitado exitosamente.",
      "insufficientBalanceTitle": "Balance Insuficiente",
      "insufficientBalanceDescription": "Se requiere un balance mínimo de {{required}} USDT. Tu balance actual es {{current}} USDT.",
      "missingCredentialsTitle": "Credenciales API Faltantes",
      "missingCredentialsDescription": "Por favor agrega y valida tus credenciales API del exchange primero.",
      "balanceVerificationFailedTitle": "Verificación de Balance Fallida",
      "balanceVerificationFailedDescription": "Falló la verificación del balance de la cuenta. Por favor verifica tus credenciales API.",
      "errorTitle": "Error de Trading Automático",
      "errorDescription": "Falló el cambio de trading automático. Por favor intenta de nuevo.",
    }
  },
  "errors": {
    "generic": "Algo salió mal. Por favor intenta de nuevo.",
    "network": "Error de red. Por favor verifica tu conexión.",
    "unauthorized": "No estás autorizado para realizar esta acción.",
    "forbidden": "Acceso denegado.",
    "notFound": "El recurso solicitado no fue encontrado.",
    "serverError": "Error del servidor. Por favor intenta más tarde.",
    "validationError": "Por favor verifica tu entrada e intenta de nuevo.",
    "walletNotConnected": "Por favor conecta tu billetera primero.",
    "insufficientBalance": "Balance insuficiente.",
    "transactionFailed": "Transacción fallida. Por favor intenta de nuevo.",
    "apiError": "Error de API. Contacta soporte si esto persiste.",
  },
  "settings": {
    "title": "Configuración",
    "profile": {
      "title": "Información del Perfil",
      "description": "Actualiza la información de tu perfil de cuenta.",
      "firstName": "Nombre",
      "lastName": "Apellido",
      "email": "Dirección de Email",
      "updateProfile": "Actualizar Perfil",
      "profileUpdated": "Perfil actualizado exitosamente",
    },
    "security": {
      "title": "Configuración de Seguridad",
      "description": "Gestiona la seguridad y autenticación de tu cuenta.",
      "currentPassword": "Contraseña Actual",
      "newPassword": "Nueva Contraseña",
      "confirmPassword": "Confirmar Nueva Contraseña",
      "changePassword": "Cambiar Contraseña",
      "passwordChanged": "Contraseña cambiada exitosamente",
      "enable2FA": "Habilitar Autenticación de Dos Factores",
      "disable2FA": "Deshabilitar Autenticación de Dos Factores",
      "twoFactorEnabled": "Autenticación de dos factores habilitada",
      "twoFactorDisabled": "Autenticación de dos factores deshabilitada",
    },
    "language": {
      "title": "Idioma y Preferencias",
      "description": "Personaliza tu idioma y preferencias de visualización.",
      "selectLanguage": "Idioma",
      "languageDescription": "Elige tu idioma preferido para la interfaz.",
    },
    "account": {
      "title": "Gestión de Cuenta",
      "description": "Gestiona la configuración y datos de tu cuenta.",
      "deleteAccount": "Eliminar Cuenta",
      "deleteAccountWarning": "Esta acción no se puede deshacer. Esto eliminará permanentemente tu cuenta y removerá tus datos de nuestros servidores.",
      "confirmDelete": "Escribe 'DELETE' para confirmar",
      "accountDeleted": "Cuenta eliminada exitosamente",
      "deactivated": {
        "title": "Cuenta Desactivada",
        "message": "Tu cuenta ha sido desactivada y el acceso a las funciones de trading está restringido.",
        "nextSteps": "Qué hacer a continuación:",
        "step1": "Contacta a nuestro equipo de soporte para asistencia",
        "step2": "Proporciona los detalles de tu cuenta y la razón de la desactivación",
        "step3": "Espera la revisión de la cuenta y reactivación",
        "emailSupport": "Soporte por Email",
        "note": "Nota:",
        "noteText": "Los datos de tu cuenta se conservan y serán restaurados al reactivarse. Las actividades de trading están temporalmente suspendidas por razones de seguridad."
      }
    }
  },
  "accessSecurity": {
    "title": "Acceso y Seguridad",
    "subtitle": "Monitorea la seguridad de tu cuenta y actividad de inicio de sesión",
    "totalLogins": "Inicios de Sesión Totales",
    "successRate": "Tasa de Éxito",
    "uniqueIPs": "IPs Únicas",
    "twoFactorStatus": "Estado 2FA",
    "enabled": "Habilitado",
    "disabled": "Deshabilitado",
    "currentSession": "Sesión Actual",
    "currentSessionDescription": "Información sobre tu sesión de inicio actual",
    "recentLoginActivity": "Actividad de Inicio Reciente",
    "recentLoginDescription": "Tus intentos de inicio de sesión y sesiones recientes",
    "refresh": "Actualizar",
    "ipAddress": "Dirección IP",
    "timestamp": "Marca de Tiempo",
    "status": "Estado",
    "userAgent": "Agente de Usuario",
    "successful": "Exitoso",
    "failed": "Fallido",
    "noLoginActivity": "No se encontró actividad de inicio de sesión",
    "currentDevice": "Dispositivo Actual",
    "location": "Ubicación",
    "sessionStart": "Inicio de Sesión",
  },
  "tiers": {
    "title": "Gestión de Niveles",
    "currentTier": "Nivel Actual",
    "choosePlan": "Elige Tu Plan",
    "tier1": {
      "name": "Nivel 1",
      "subtitle": "Gratis",
      "description": "Perfecto para comenzar con señales básicas de trading",
      "features": [
        "Señales básicas de trading",
        "Ejecución manual de operaciones",
        "Soporte de la comunidad",
        "Análisis básico del mercado"
      ]
    },
    "tier2": {
      "name": "Nivel 2",
      "subtitle": "Acceso por 30 días",
      "description": "Características avanzadas para traders serios",
      "features": [
        "Señales avanzadas de trading con IA",
        "Capacidades de auto-trading",
        "Soporte prioritario",
        "Análisis avanzado",
        "Herramientas de gestión de riesgo"
      ]
    },
    "tier3": {
      "name": "Nivel 3",
      "subtitle": "NFT Requerido",
      "description": "Nivel premium con características exclusivas",
      "features": [
        "Señales premium de trading",
        "Auto-trading avanzado",
        "Soporte VIP",
        "Insights exclusivos del mercado",
        "Estrategias personalizadas"
      ]
    },
    "activate": "Activar",
    "current": "Actual",
    "paymentRequired": "Pago Requerido",
    "nftRequired": "NFT Requerido",
    "membershipStatus": "Estado de Membresía",
    "active": "Activo",
    "expired": "Expirado",
    "daysRemaining": "Días restantes",
    "expiresOn": "Expira el",
  },
  "apiCredentials": {
    "title": "Credenciales API",
    "description": "Gestiona tus credenciales API de intercambio para trading",
    "addCredentials": "Agregar Credenciales API",
    "exchange": "Intercambio",
    "exchanger": "Intercambiador",
    "apiKey": "Clave API",
    "apiSecret": "Secreto API",
    "secretKey": "Clave Secreta",
    "passphrase": "Frase de Paso",
    "testConnection": "Probar Conexión",
    "save": "Guardar Credenciales",
    "edit": "Editar",
    "delete": "Eliminar",
    "status": "Estado",
    "connected": "Conectado",
    "disconnected": "Desconectado",
    "invalid": "Inválido",
    "active": "Activo",
    "testing": "Probando...",
    "connectionSuccess": "Conexión exitosa",
    "connectionFailed": "Conexión fallida",
    "credentialsAdded": "Credenciales API agregadas exitosamente",
    "credentialsUpdated": "Credenciales API actualizadas exitosamente",
    "credentialsDeleted": "Credenciales API eliminadas exitosamente",
    "credentialAddedLimitReached": "Credencial agregada (límite alcanzado)",
    "requiredMarketType": "Tipo de Mercado Requerido",
    "connectedTo": "Conectado a",
    "connectionNeedsAttention": "La conexión a",
    "needsAttention": "necesita atención",
    "lastUpdated": "Última actualización",
    "updateCredentialsMessage": "Por favor actualiza tus credenciales API para continuar trading",
    "disconnect": "Desconectar",
    "disconnecting": "Desconectando...",
    "setAsActive": "Establecer como Activo",
    "activating": "Activando...",
    "autoTrading": "Trading Automático",
    "enableAutoTradingDesc": "Habilitar trading automatizado para este intercambio",
    "tier2LimitReached": "Límite de Nivel 2 alcanzado: hasta dos credenciales CEX permitidas.",
    "add": "Agregar",
    "update": "Actualizar",
    "connectionValidated": "¡Conexión validada!",
    "autoTradingStatusChanged": "El trading automático ha sido",
    "marketTypes": {
      "futures": "Futuros",
      "spot": "Spot",
      "binanceNote": "Trading de futuros - apalancamiento hasta 125x disponible",
      "binanceUsNote": "Trading spot con margen - apalancamiento hasta 3x disponible",
      "krakenNote": "Trading de futuros - apalancamiento hasta 100x disponible",
      "bybitNote": "Trading de futuros - apalancamiento hasta 100x disponible",
      "hyperliquidNote": "Trading de futuros perpetuos - apalancamiento hasta 50x disponible",
      "standardNote": "Trading spot estándar",
    },
    "errors": {
      "loadFailed": "Error al cargar credenciales",
      "keysRequired": "Se requieren Clave API y Clave Secreta",
      "tierUndetermined": "No se puede determinar el nivel de usuario",
      "tier1Limit": "Nivel 1 permite solo una credencial API",
      "tier2PerCex": "Nivel 2 permite solo una credencial por CEX",
      "tier2Limit": "Nivel 2 permite hasta dos credenciales (una por CEX)",
      "tier3Limit": "Nivel 3 permite solo una credencial para todos los CEXs",
      "saveFailed": "Error al guardar",
      "validationFailed": "Validación fallida",
      "activationFailed": "Activación fallida",
      "deactivationFailed": "Desactivación fallida",
      "noCredentials": "No se encontraron credenciales para este intercambio",
      "autoTradingUpdateFailed": "Error al Actualizar Trading Automático",
      "autoTradingUpdateFailedDesc": "Error al actualizar configuración de trading automático",
    },
    "success": {
      "credentialsSaved": "Credenciales guardadas",
      "connectionSuccessful": "Conexión exitosa",
      "activated": "Activado",
      "deactivated": "Desactivado",
      "autoTradingEnabled": "Trading Automático Habilitado",
      "autoTradingDisabled": "Trading Automático Deshabilitado",
    }
  },
  "referrals": {
    "title": "Panel de Referencias",
    "yourReferralCode": "Tu Código de Referencia",
    "referralLink": "Enlace de Referencia",
    "copyLink": "Copiar Enlace",
    "linkCopied": "Enlace copiado al portapapeles",
    "totalReferrals": "Total de Referencias",
    "activeReferrals": "Referencias Activas",
    "totalEarnings": "Ganancias Totales",
    "pendingPayouts": "Pagos Pendientes",
    "referralHistory": "Historial de Referencias",
    "noReferrals": "Aún no hay referencias",
    "inviteFriends": "Invita amigos y gana recompensas",
  },
  "help": {
    "title": "Ayuda y Soporte",
    "faq": "Preguntas Frecuentes",
    "legal": "Legal",
    "contact": "Contacto",
    "searchFAQ": "Buscar en FAQ...",
    "categories": {
      "general": "General",
      "trading": "Trading",
      "tiers": "Niveles",
      "setup": "Configuración",
      "security": "Seguridad",
      "billing": "Facturación",
    },
    "contactSupport": "Contactar Soporte",
    "email": "Email",
    "subject": "Asunto",
    "message": "Mensaje",
    "sendMessage": "Enviar Mensaje",
    "messageSent": "Mensaje enviado exitosamente",
  },
  "paperTradingHelp": {
    "title": "Guía de Trading de Simulación",
    "whatIsPaperTrading": "¿Qué es el Trading de Simulación?",
    "whatIsPaperTradingDesc": "El trading de simulación es un entorno de trading simulado que te permite practicar estrategias de trading usando dinero virtual en lugar de fondos reales. Es la forma perfecta de probar nuestro sistema de trading con IA sin ningún riesgo financiero.",
    "keyBenefits": "Beneficios Clave",
    "riskFree": "Aprendizaje Sin Riesgo",
    "riskFreeDesc": "Practica con fondos virtuales - sin dinero real en riesgo",
    "realMarketData": "Datos de Mercado Reales",
    "realMarketDataDesc": "Experimenta condiciones de mercado en vivo y movimientos de precios",
    "aiTradingSystem": "Sistema de Trading con IA",
    "aiTradingSystemDesc": "Prueba nuestras señales avanzadas de IA en un entorno seguro",
    "buildConfidence": "Construye Confianza",
    "buildConfidenceDesc": "Desarrolla tus habilidades de trading antes de usar dinero real",
    "howItWorks": "Cómo Funciona",
    "step1": "Cambiar a Modo Simulación",
    "step1Desc": "Cambia el interruptor de modo de trading para habilitar el trading de simulación",
    "step2": "Balance Virtual",
    "step2Desc": "Comienza con $10,000 de balance virtual (se puede reiniciar)",
    "step3": "Señales de IA",
    "step3Desc": "Nuestra IA genera señales de trading basadas en datos de mercado reales",
    "step4": "Operaciones Simuladas",
    "step4Desc": "Las operaciones se ejecutan virtualmente - rastrea tu rendimiento",
    "importantNotes": "Notas Importantes",
    "noRealMoney": "Sin Dinero Real",
    "noRealMoneyDesc": "El trading de simulación usa solo fondos virtuales. No ocurren ganancias o pérdidas reales.",
    "noRealFees": "Sin Comisiones Reales",
    "noRealFeesDesc": "Las comisiones de trading y el deslizamiento se simulan para una experiencia realista.",
    "performanceTracking": "Seguimiento de Rendimiento",
    "performanceTrackingDesc": "Todas las estadísticas y análisis se basan únicamente en operaciones simuladas.",
    "readyToStart": "¿Listo para Comenzar?",
    "readyToStartDesc": "¡Cambia el interruptor de \"Modo de Trading\" a \"Simulación\" y comienza a practicar con fondos virtuales hoy!",
    "buildConfidenceFooter": "💪 ¡Construye confianza con el trading de simulación antes de arriesgar dinero real!",
    "resetModal": {
      "title": "Reiniciar Cuenta de Trading de Simulación",
      "warning": "Advertencia: Esta acción no se puede deshacer",
      "warningDesc": "Esto eliminará permanentemente todo tu historial de trading de simulación y reiniciará tu balance virtual.",
      "resetLimitReached": "Límite Diario de Reinicio Alcanzado",
      "resetLimitReachedDesc": "Has usado los 3 reinicios para hoy. El límite se reinicia a medianoche UTC.",
      "resetsRemaining": "Reinicios restantes hoy: {{count},",
      "currentBalance": "Balance Actual: ${{balance},",
      "resetToDefault": "Reiniciar a $10,000 por defecto",
      "customBalance": "Establecer balance personalizado",
      "customBalanceDesc": "Ingresa un balance inicial personalizado (mínimo $1,000)",
      "confirmationRequired": "Escribe 'RESET' para confirmar",
      "confirmationPlaceholder": "Escribe RESET aquí...",
      "confirmationRequiredError": "Por favor escribe 'RESET' para confirmar",
      "invalidBalance": "Balance Inválido",
      "invalidBalanceDesc": "Por favor ingresa un monto de balance positivo válido",
      "resetting": "Reiniciando...",
      "resetButton": "Reiniciar Cuenta",
    },
    "howItWorksModal": {
      "title": "Cómo Funciona el Trading de Simulación",
      "step1": "Comienza con $10,000 de balance virtual (configurable)",
      "step2": "Habilita el trading automático para que la IA ejecute operaciones automáticamente",
      "step3": "Monitorea el rendimiento y ajusta la configuración de riesgo según sea necesario",
      "step4": "Cambia al trading en vivo cuando tengas confianza en el sistema",
    },
    "importantNotesDetails": {
      "noRealMoneyTitle": "Sin Dinero Real:",
      "noRealMoneyDesc": "El trading de simulación usa solo fondos virtuales. No ocurren ganancias o pérdidas reales.",
      "noFeesTitle": "Sin Comisiones:",
      "noFeesDesc": "El trading de simulación no genera comisiones de participación en ganancias ya que no se obtienen ganancias reales.",
      "resetLimitTitle": "Límite de Reinicio:",
      "resetLimitDesc": "Puedes reiniciar tu cuenta de simulación hasta 3 veces por día.",
    },
    "benefits": {
      "title": "Beneficios del Trading de Simulación",
      "riskFreeLearning": "Aprendizaje Sin Riesgo",
      "riskFreeLearningDesc": "Practica sin perder dinero real mientras aprendes cómo funciona nuestro sistema de IA",
      "strategyTesting": "Prueba de Estrategias",
      "strategyTestingDesc": "Prueba diferentes configuraciones de riesgo y ve cómo funcionan con el tiempo",
      "realMarketData": "Datos de Mercado Reales",
      "realMarketDataDesc": "Usa precios de mercado en vivo y pronósticos de ML reales para una experiencia auténtica",
    },
    "gettingStarted": {
      "title": "¿Listo para Comenzar?",
      "description": "¡Cambia el interruptor \"Modo de Trading\" a \"Simulación\" y comienza a practicar con fondos virtuales hoy!",
      "buildConfidenceFooter": "💪 ¡Construye confianza con el trading de simulación antes de arriesgar dinero real!",
    },
    "perfectForBeginners": "💡 ¡Perfecto para principiantes y traders experimentados que quieren probar nuevas estrategias!",
    "availableToAllTitle": "Disponible para Todos:",
    "availableToAllDesc": "El trading de simulación está disponible para todos los usuarios independientemente del nivel de tier.",
    "gotItThanks": "¡Entendido, gracias!",
  },
  "paperTradingAnalytics": {
    "title": "Análisis de Trading de Simulación",
    "refresh": "Actualizar",
    "periods": {
      "7d": "7D",
      "30d": "30D",
      "90d": "90D",
    },
    "metrics": {
      "totalPnl": "P&L Total",
      "winRate": "Tasa de Éxito",
      "totalTrades": "Operaciones Totales",
      "profitFactor": "Factor de Ganancia",
    },
    "tradeBreakdown": {
      "title": "Desglose de Operaciones",
      "winningTrades": "Operaciones Ganadoras",
      "losingTrades": "Operaciones Perdedoras",
    },
    "averagePerformance": {
      "title": "Rendimiento Promedio",
      "averageWin": "Ganancia Promedio",
      "averageLoss": "Pérdida Promedio",
      "riskRewardRatio": "Relación Riesgo/Recompensa",
    },
    "balanceHistory": {
      "title": "Historial de Balance",
      "chartVisualization": "Visualización del gráfico de balance",
      "current": "Actual",
    },
    "errors": {
      "loadFailed": "Error al cargar datos de análisis",
    }
  },
  "paperTradingDashboard": {
    "performance": {
      "title": "Rendimiento del Trading de Simulación",
      "refresh": "Actualizar",
      "reset": "Reiniciar",
      "totalReturn": "Retorno Total",
    },
    "analytics": {
      "thirtyDay": "Análisis de 30 Días",
    },
    "errors": {
      "loadFailed": "Error al cargar datos del trading de simulación",
    }
  }
};
