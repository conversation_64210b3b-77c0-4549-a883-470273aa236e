/**
 * Paper Trading Analytics Component
 * 
 * Comprehensive analytics and reporting for paper trading performance
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  DollarSign,
  Target,
  Activity,
  RefreshCw
} from 'lucide-react';
import { paperTradingApi } from '../services/paperTradingApi';
import { usePaperTrading } from '../hooks/usePaperTrading';
import { useTranslation } from '@/hooks/useTranslation';
import { useMobile } from '../hooks/useResponsiveDesign';
import { toast } from '@/components/ui/use-toast';

interface PaperTradingAnalyticsProps {
  className?: string;
}

export const PaperTradingAnalytics: React.FC<PaperTradingAnalyticsProps> = ({ className = '' }) => {
  const { t } = useTranslation();
  const { isMobile } = useMobile();
  const { isPaperMode, account } = usePaperTrading();
  
  const [analytics, setAnalytics] = useState<any>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<number>(30);
  const [isLoading, setIsLoading] = useState(false);

  // Load analytics data
  const loadAnalytics = async (days: number = selectedPeriod) => {
    if (!isPaperMode) return;
    
    try {
      setIsLoading(true);
      const data = await paperTradingApi.getPaperTradingAnalytics(days);
      setAnalytics(data);
    } catch (error) {
      console.error('Error loading paper trading analytics:', error);
      toast({
        title: "Error",
        description: t('paperTradingAnalytics.errors.loadFailed'),
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle period change
  const handlePeriodChange = (days: number) => {
    setSelectedPeriod(days);
    loadAnalytics(days);
  };

  // Load data when component mounts or paper mode changes
  useEffect(() => {
    loadAnalytics();
  }, [isPaperMode]);

  // Don't render if not in paper mode
  if (!isPaperMode) {
    return null;
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const getPerformanceColor = (value: number) => {
    if (value > 0) return 'text-green-600 dark:text-green-400';
    if (value < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  const periods = [
    { label: t('paperTradingAnalytics.periods.7d'), days: 7 },
    { label: t('paperTradingAnalytics.periods.30d'), days: 30 },
    { label: t('paperTradingAnalytics.periods.90d'), days: 90 }
  ];

  return (
    <div className={`space-y-6 w-full min-w-0 max-w-full overflow-hidden ${className}`}>
      {/* Header */}
      <div className={`flex ${isMobile ? 'flex-col gap-3' : 'items-center justify-between'} w-full`}>
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <h2 className={`${isMobile ? 'text-base' : 'text-xl'} font-semibold flex items-center min-w-0`}>
            <BarChart3 className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} mr-2 text-orange-600 flex-shrink-0`} />
            <span className="truncate">{t('paperTradingAnalytics.title')}</span>
          </h2>
          <span className="text-xs bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded flex-shrink-0">
            {t('trading.paperTradingLabel')}
          </span>
        </div>
        <div className={`flex items-center ${isMobile ? 'gap-1' : 'space-x-2'} flex-shrink-0`}>
          {/* Period Selector */}
          <div className={`flex bg-gray-100 dark:bg-gray-800 rounded-lg ${isMobile ? 'p-0.5' : 'p-1'} w-full min-w-0 max-w-full overflow-hidden`}>
            {periods.map((period) => (
              <button
                key={period.days}
                onClick={() => handlePeriodChange(period.days)}
                className={`${isMobile ? 'px-2 py-1 text-xs' : 'px-3 py-1 text-xs'} font-medium rounded-md transition-colors flex-1 min-w-0 truncate ${
                  selectedPeriod === period.days
                    ? 'bg-orange-500 text-white'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                }`}
              >
                {period.label}
              </button>
            ))}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadAnalytics()}
            disabled={isLoading}
            className={isMobile ? 'text-xs px-2 py-1' : ''}
          >
            <RefreshCw className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'} ${isLoading ? 'animate-spin' : ''}`} />
            {!isMobile && <span className="ml-1">{t('paperTradingAnalytics.refresh')}</span>}
          </Button>
        </div>
      </div>

      {analytics ? (
        <>
          {/* Performance Summary */}
          <div className={`grid ${isMobile ? 'grid-cols-2 gap-3' : 'grid-cols-4 gap-4'}`}>
            <Card>
              <CardContent className={`${isMobile ? 'p-4' : 'p-6'} text-center`}>
                <div className="flex items-center justify-center mb-2">
                  <DollarSign className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} text-blue-600`} />
                </div>
                <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>{t('paperTradingAnalytics.metrics.totalPnl')}</p>
                <p className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold ${getPerformanceColor(analytics.summary.total_pnl)}`}>
                  {formatCurrency(analytics.summary.total_pnl)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className={`${isMobile ? 'p-4' : 'p-6'} text-center`}>
                <div className="flex items-center justify-center mb-2">
                  <Target className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} text-green-600`} />
                </div>
                <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>{t('paperTradingAnalytics.metrics.winRate')}</p>
                <p className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold`}>
                  {analytics.summary.win_rate.toFixed(1)}%
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className={`${isMobile ? 'p-4' : 'p-6'} text-center`}>
                <div className="flex items-center justify-center mb-2">
                  <Activity className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} text-purple-600`} />
                </div>
                <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>{t('paperTradingAnalytics.metrics.totalTrades')}</p>
                <p className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold`}>
                  {analytics.summary.total_trades}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className={`${isMobile ? 'p-4' : 'p-6'} text-center`}>
                <div className="flex items-center justify-center mb-2">
                  <TrendingUp className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'} text-orange-600`} />
                </div>
                <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground`}>{t('paperTradingAnalytics.metrics.profitFactor')}</p>
                <p className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold`}>
                  {analytics.summary.profit_factor.toFixed(2)}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Metrics */}
          <div className={`grid ${isMobile ? 'grid-cols-1 gap-4' : 'grid-cols-2 gap-6'}`}>
            {/* Trade Breakdown */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'}`}>{t('paperTradingAnalytics.tradeBreakdown.title')}</CardTitle>
                  <span className="text-xs bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded">
                    {t('trading.paperTradingLabel')}
                  </span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className={`${isMobile ? 'text-sm' : 'text-base'}`}>{t('paperTradingAnalytics.tradeBreakdown.winningTrades')}</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-green-600">
                        {analytics.summary.winning_trades}
                      </span>
                      <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full" 
                          style={{ 
                            width: `${analytics.summary.total_trades > 0 ? (analytics.summary.winning_trades / analytics.summary.total_trades) * 100 : 0}%` 
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className={`${isMobile ? 'text-sm' : 'text-base'}`}>{t('paperTradingAnalytics.tradeBreakdown.losingTrades')}</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-red-600">
                        {analytics.summary.losing_trades}
                      </span>
                      <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-red-500 h-2 rounded-full" 
                          style={{ 
                            width: `${analytics.summary.total_trades > 0 ? (analytics.summary.losing_trades / analytics.summary.total_trades) * 100 : 0}%` 
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Average Performance */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'}`}>{t('paperTradingAnalytics.averagePerformance.title')}</CardTitle>
                  <span className="text-xs bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded">
                    {t('trading.paperTradingLabel')}
                  </span>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className={`${isMobile ? 'text-sm' : 'text-base'}`}>{t('paperTradingAnalytics.averagePerformance.averageWin')}</span>
                    <span className="font-semibold text-green-600">
                      {formatCurrency(analytics.summary.average_win)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className={`${isMobile ? 'text-sm' : 'text-base'}`}>{t('paperTradingAnalytics.averagePerformance.averageLoss')}</span>
                    <span className="font-semibold text-red-600">
                      {formatCurrency(analytics.summary.average_loss)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center pt-2 border-t">
                    <span className={`${isMobile ? 'text-sm' : 'text-base'} font-medium`}>{t('paperTradingAnalytics.averagePerformance.riskRewardRatio')}</span>
                    <span className="font-bold">
                      1:{analytics.summary.profit_factor.toFixed(2)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Balance History Chart Placeholder */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'}`}>{t('paperTradingAnalytics.balanceHistory.title')}</CardTitle>
                <span className="text-xs bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 px-2 py-1 rounded">
                  {t('trading.paperTradingLabel')}
                </span>
              </div>
            </CardHeader>
            <CardContent>
              {analytics.balance_history && analytics.balance_history.length > 0 ? (
                <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">{t('paperTradingAnalytics.balanceHistory.chartVisualization')}</p>
                    <p className="text-xs text-gray-400 mt-1">
                      {t('paperTradingAnalytics.balanceHistory.current')}: {formatCurrency(analytics.current_balance)}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="h-32 flex items-center justify-center text-gray-500">
                  <p className="text-sm">No balance history data available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      ) : (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-600 mx-auto mb-4"></div>
            <p className="text-sm text-gray-500">Loading analytics...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaperTradingAnalytics;
