/**
 * Responsive Design Hook for DeepTrade Application
 * 
 * Provides responsive breakpoint detection and utilities for mobile-first design.
 * Supports real-time screen size monitoring and breakpoint-based rendering.
 */

import { useState, useEffect, useMemo } from 'react';

interface BreakpointConfig {
  xs: number;  // Extra small devices
  sm: number;  // Small devices
  md: number;  // Medium devices
  lg: number;  // Large devices
  xl: number;  // Extra large devices
  xxl: number; // Extra extra large devices
}

interface ScreenSize {
  width: number;
  height: number;
}

interface ResponsiveState {
  screenSize: ScreenSize;
  currentBreakpoint: string;
  isXs: boolean;
  isSm: boolean;
  isMd: boolean;
  isLg: boolean;
  isXl: boolean;
  isXxl: boolean;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeDesktop: boolean;
  orientation: 'portrait' | 'landscape';
}

const defaultBreakpoints: BreakpointConfig = {
  xs: 0,     // 0px and up
  sm: 640,   // 640px and up (mobile)
  md: 768,   // 768px and up (tablet)
  lg: 1024,  // 1024px and up (desktop)
  xl: 1280,  // 1280px and up (large desktop)
  xxl: 1536, // 1536px and up (extra large desktop)
};

export const useResponsiveDesign = (customBreakpoints?: Partial<BreakpointConfig>) => {
  // Use useMemo to prevent breakpoints object from changing on every render
  const breakpoints = useMemo(() => ({ ...defaultBreakpoints, ...customBreakpoints }), [customBreakpoints]);

  const [responsiveState, setResponsiveState] = useState<ResponsiveState>(() => {
    const initialWidth = typeof window !== 'undefined' ? window.innerWidth : 1024;
    const initialHeight = typeof window !== 'undefined' ? window.innerHeight : 768;

    return {
      screenSize: { width: initialWidth, height: initialHeight },
      currentBreakpoint: getCurrentBreakpoint(initialWidth, breakpoints),
      isXs: initialWidth >= breakpoints.xs && initialWidth < breakpoints.sm,
      isSm: initialWidth >= breakpoints.sm && initialWidth < breakpoints.md,
      isMd: initialWidth >= breakpoints.md && initialWidth < breakpoints.lg,
      isLg: initialWidth >= breakpoints.lg && initialWidth < breakpoints.xl,
      isXl: initialWidth >= breakpoints.xl && initialWidth < breakpoints.xxl,
      isXxl: initialWidth >= breakpoints.xxl,
      isMobile: initialWidth < breakpoints.md,
      isTablet: initialWidth >= breakpoints.md && initialWidth < breakpoints.lg,
      isDesktop: initialWidth >= breakpoints.lg && initialWidth < breakpoints.xxl,
      isLargeDesktop: initialWidth >= breakpoints.xxl,
      orientation: initialWidth > initialHeight ? 'landscape' : 'portrait',
    };
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const currentBreakpoint = getCurrentBreakpoint(width, breakpoints);

      setResponsiveState({
        screenSize: { width, height },
        currentBreakpoint,
        isXs: width >= breakpoints.xs && width < breakpoints.sm,
        isSm: width >= breakpoints.sm && width < breakpoints.md,
        isMd: width >= breakpoints.md && width < breakpoints.lg,
        isLg: width >= breakpoints.lg && width < breakpoints.xl,
        isXl: width >= breakpoints.xl && width < breakpoints.xxl,
        isXxl: width >= breakpoints.xxl,
        isMobile: width < breakpoints.md,
        isTablet: width >= breakpoints.md && width < breakpoints.lg,
        isDesktop: width >= breakpoints.lg && width < breakpoints.xxl,
        isLargeDesktop: width >= breakpoints.xxl,
        orientation: width > height ? 'landscape' : 'portrait',
      });
    };

    // Set initial values
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [breakpoints]);

  return responsiveState;
};

function getCurrentBreakpoint(width: number, breakpoints: BreakpointConfig): string {
  if (width >= breakpoints.xxl) return 'xxl';
  if (width >= breakpoints.xl) return 'xl';
  if (width >= breakpoints.lg) return 'lg';
  if (width >= breakpoints.md) return 'md';
  if (width >= breakpoints.sm) return 'sm';
  return 'xs';
}

/**
 * Hook for conditional rendering based on breakpoints
 */
export const useBreakpoint = (breakpoint: string) => {
  const { currentBreakpoint } = useResponsiveDesign();
  
  const breakpointOrder = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);
  const targetIndex = breakpointOrder.indexOf(breakpoint);
  
  return {
    isExact: currentBreakpoint === breakpoint,
    isAbove: currentIndex > targetIndex,
    isBelow: currentIndex < targetIndex,
    isAtOrAbove: currentIndex >= targetIndex,
    isAtOrBelow: currentIndex <= targetIndex,
  };
};

/**
 * Hook for mobile-specific functionality
 */
export const useMobile = () => {
  const { isMobile, isXs, isSm, orientation } = useResponsiveDesign();
  
  return {
    isMobile,
    isExtraSmall: isXs,
    isSmall: isSm,
    isPortrait: orientation === 'portrait',
    isLandscape: orientation === 'landscape',
  };
};

/**
 * Hook for tablet-specific functionality
 */
export const useTablet = () => {
  const { isTablet, isMd, orientation } = useResponsiveDesign();
  
  return {
    isTablet,
    isMedium: isMd,
    isPortrait: orientation === 'portrait',
    isLandscape: orientation === 'landscape',
  };
};

/**
 * Hook for desktop-specific functionality
 */
export const useDesktop = () => {
  const { isDesktop, isLargeDesktop, isLg, isXl, isXxl } = useResponsiveDesign();
  
  return {
    isDesktop: isDesktop || isLargeDesktop,
    isLarge: isLg,
    isExtraLarge: isXl,
    isExtraExtraLarge: isXxl,
    isLargeDesktop,
  };
};

/**
 * Utility function to get responsive classes
 */
export const getResponsiveClasses = (
  classes: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
    xxl?: string;
    default?: string;
  }
) => {
  const { currentBreakpoint } = useResponsiveDesign();
  
  // Return the class for current breakpoint or fall back to smaller breakpoints
  const breakpointOrder = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);
  
  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const bp = breakpointOrder[i] as keyof typeof classes;
    if (classes[bp]) {
      return classes[bp];
    }
  }
  
  return classes.default || '';
};

/**
 * Utility function for responsive values
 */
export const useResponsiveValue = <T>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  xxl?: T;
  default: T;
}): T => {
  const { currentBreakpoint } = useResponsiveDesign();
  
  // Return the value for current breakpoint or fall back to smaller breakpoints
  const breakpointOrder = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);
  
  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const bp = breakpointOrder[i] as keyof typeof values;
    if (values[bp] !== undefined) {
      return values[bp] as T;
    }
  }
  
  return values.default;
};

export default useResponsiveDesign;
