import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, Link } from 'react-router-dom';
import { Button } from '@/components/ui/Button';
import { toastSuccess, toastError } from '@/components/ui/use-toast';
import { Check<PERSON>ircle, XCircle, Loader2 } from 'lucide-react';
import { TermsOfServiceModal } from '@/components/modals/TermsOfServiceModal';
import { PrivacyPolicyModal } from '@/components/modals/PrivacyPolicyModal';

export default function VerifyEmailChange() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isVerifying, setIsVerifying] = useState(true);
  const [verificationResult, setVerificationResult] = useState<{
    success: boolean;
    message: string;
    newEmail?: string;
  } | null>(null);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);

  useEffect(() => {
    const token = searchParams.get('token');
    
    if (!token) {
      setVerificationResult({
        success: false,
        message: 'Invalid verification link. No token provided.',
      });
      setIsVerifying(false);
      return;
    }

    verifyEmailChange(token);
  }, [searchParams]);

  const verifyEmailChange = async (token: string) => {
    try {
      const response = await fetch('http://localhost:5000/api/users/email/verify', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();

      if (response.ok) {
        setVerificationResult({
          success: true,
          message: data.message,
          newEmail: data.new_email,
        });
        
        toastSuccess({
          title: 'Email Updated',
          description: 'Your email address has been successfully updated.',
        });
      } else {
        setVerificationResult({
          success: false,
          message: data.error || 'Email verification failed',
        });
        
        toastError({
          title: 'Verification Failed',
          description: data.error || 'Email verification failed',
        });
      }
    } catch (error) {
      console.error('Email verification error:', error);
      setVerificationResult({
        success: false,
        message: 'Network error. Please try again later.',
      });
      
      toastError({
        title: 'Network Error',
        description: 'Failed to verify email. Please try again later.',
      });
    } finally {
      setIsVerifying(false);
    }
  };

  if (isVerifying) {
    return (
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
        <div className="flex flex-col space-y-2 text-center">
          <div className="flex justify-center mb-4">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
          </div>
          <h1 className="text-2xl font-semibold tracking-tight">Verifying Email Change</h1>
          <p className="text-sm text-muted-foreground">
            Please wait while we verify your new email address...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
      <div className="flex flex-col space-y-2 text-center">
        <div className="flex justify-center mb-4">
          {verificationResult?.success ? (
            <CheckCircle className="h-12 w-12 text-green-600" />
          ) : (
            <XCircle className="h-12 w-12 text-red-600" />
          )}
        </div>
        
        <h1 className="text-2xl font-semibold tracking-tight">
          {verificationResult?.success ? 'Email Updated Successfully' : 'Email Verification Failed'}
        </h1>
        
        <p className="text-sm text-muted-foreground">
          {verificationResult?.message}
        </p>
        
        {verificationResult?.success && verificationResult.newEmail && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-800">
              Your email has been updated to: <strong>{verificationResult.newEmail}</strong>
            </p>
          </div>
        )}
      </div>
      
      <div className="grid gap-4">
        {verificationResult?.success ? (
          <>
            <Button
              onClick={() => navigate('/settings')}
              className="w-full"
            >
              Go to Settings
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="w-full"
            >
              Go to Dashboard
            </Button>
          </>
        ) : (
          <>
            <Button
              onClick={() => navigate('/settings')}
              className="w-full"
            >
              Try Again
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="w-full"
            >
              Go to Dashboard
            </Button>
          </>
        )}
        
        <div className="text-center space-y-2">
          <Link
            to="/login"
            className="text-sm text-primary hover:underline"
          >
            Need to log in?
          </Link>
          <p className="text-xs text-muted-foreground">
            By using our service, you agree to our{' '}
            <button
              type="button"
              onClick={() => setShowTermsModal(true)}
              className="text-primary hover:underline"
            >
              Terms of Service
            </button>{' '}
            and{' '}
            <button
              type="button"
              onClick={() => setShowPrivacyModal(true)}
              className="text-primary hover:underline"
            >
              Privacy Policy
            </button>
            .
          </p>
        </div>
      </div>

      {/* Terms of Service Modal */}
      <TermsOfServiceModal
        isOpen={showTermsModal}
        onClose={() => setShowTermsModal(false)}
      />

      {/* Privacy Policy Modal */}
      <PrivacyPolicyModal
        isOpen={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
      />
    </div>
  );
}
