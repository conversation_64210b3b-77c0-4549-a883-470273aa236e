/**
 * Clock Synchronization Manager
 * 
 * Handles client-server clock synchronization to prevent JW<PERSON> "Token used too early" errors.
 * This utility detects clock drift between client and server and provides synchronized timestamps.
 */

interface ClockSyncResult {
  drift: number;
  serverTime: number;
  clientTime: number;
  synchronized: boolean;
  roundTripTime: number;
}

interface SyncAttempt {
  timestamp: number;
  drift: number;
  roundTripTime: number;
}

export class ClockSyncManager {
  private static instance: ClockSyncManager;
  private clockDrift: number = 0;
  private lastSyncTime: number = 0;
  private readonly SYNC_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_DRIFT_TOLERANCE = 30000; // 30 seconds
  private readonly MAX_ROUND_TRIP_TIME = 5000; // 5 seconds
  private readonly SYNC_ATTEMPTS = 3; // Number of sync attempts to average
  private syncHistory: SyncAttempt[] = [];
  
  static getInstance(): ClockSyncManager {
    if (!ClockSyncManager.instance) {
      ClockSyncManager.instance = new ClockSyncManager();
    }
    return ClockSyncManager.instance;
  }
  
  /**
   * Perform clock synchronization with the server
   */
  async syncClock(): Promise<ClockSyncResult> {
    try {
      console.log('[CLOCK_SYNC] Starting clock synchronization...');
      
      const attempts: SyncAttempt[] = [];
      
      // Perform multiple sync attempts to get accurate measurement
      for (let i = 0; i < this.SYNC_ATTEMPTS; i++) {
        const attempt = await this.performSyncAttempt();
        if (attempt.roundTripTime < this.MAX_ROUND_TRIP_TIME) {
          attempts.push(attempt);
        }
        
        // Small delay between attempts
        if (i < this.SYNC_ATTEMPTS - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
      
      if (attempts.length === 0) {
        throw new Error('All sync attempts failed or had high latency');
      }
      
      // Use the attempt with the lowest round trip time for best accuracy
      const bestAttempt = attempts.reduce((best, current) => 
        current.roundTripTime < best.roundTripTime ? current : best
      );
      
      this.clockDrift = bestAttempt.drift;
      this.lastSyncTime = Date.now();
      
      // Store in sync history
      this.syncHistory.push(bestAttempt);
      if (this.syncHistory.length > 10) {
        this.syncHistory.shift(); // Keep only last 10 attempts
      }
      
      const result: ClockSyncResult = {
        drift: this.clockDrift,
        serverTime: bestAttempt.timestamp + bestAttempt.drift,
        clientTime: bestAttempt.timestamp,
        synchronized: Math.abs(this.clockDrift) < this.MAX_DRIFT_TOLERANCE,
        roundTripTime: bestAttempt.roundTripTime
      };
      
      console.log('[CLOCK_SYNC] Synchronization complete:', {
        drift: this.clockDrift,
        synchronized: result.synchronized,
        roundTripTime: bestAttempt.roundTripTime,
        attempts: attempts.length
      });
      
      return result;
      
    } catch (error) {
      console.error('[CLOCK_SYNC] Clock sync failed:', error);
      throw error;
    }
  }
  
  /**
   * Perform a single sync attempt
   */
  private async performSyncAttempt(): Promise<SyncAttempt> {
    const clientStartTime = Date.now();
    
    const response = await fetch('/api/auth/server-time', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    const clientEndTime = Date.now();
    const roundTripTime = clientEndTime - clientStartTime;
    
    if (!response.ok) {
      throw new Error(`Server time request failed: ${response.status}`);
    }
    
    const data = await response.json();
    const serverTime = data.timestamp;
    
    // Estimate server time when request was processed (middle of round trip)
    const estimatedServerTime = serverTime + (roundTripTime / 2);
    const clientMidTime = clientStartTime + (roundTripTime / 2);
    
    const drift = estimatedServerTime - clientMidTime;
    
    return {
      timestamp: clientMidTime,
      drift,
      roundTripTime
    };
  }
  
  /**
   * Get current time synchronized with server
   */
  getSynchronizedTime(): number {
    return Date.now() + this.clockDrift;
  }
  
  /**
   * Check if synchronization is needed
   */
  needsSync(): boolean {
    return Date.now() - this.lastSyncTime > this.SYNC_INTERVAL;
  }
  
  /**
   * Check if clock is currently synchronized
   */
  isSynchronized(): boolean {
    return Math.abs(this.clockDrift) < this.MAX_DRIFT_TOLERANCE && 
           !this.needsSync();
  }
  
  /**
   * Get current clock drift in milliseconds
   */
  getClockDrift(): number {
    return this.clockDrift;
  }
  
  /**
   * Get sync statistics
   */
  getSyncStats(): {
    lastSyncTime: number;
    clockDrift: number;
    synchronized: boolean;
    syncHistory: SyncAttempt[];
  } {
    return {
      lastSyncTime: this.lastSyncTime,
      clockDrift: this.clockDrift,
      synchronized: this.isSynchronized(),
      syncHistory: [...this.syncHistory]
    };
  }
  
  /**
   * Force immediate synchronization
   */
  async forceSyncNow(): Promise<ClockSyncResult> {
    console.log('[CLOCK_SYNC] Force sync requested');
    return await this.syncClock();
  }
  
  /**
   * Reset synchronization state
   */
  reset(): void {
    this.clockDrift = 0;
    this.lastSyncTime = 0;
    this.syncHistory = [];
    console.log('[CLOCK_SYNC] Synchronization state reset');
  }
}

/**
 * Utility function to get synchronized timestamp
 */
export const getSynchronizedTimestamp = (): number => {
  return ClockSyncManager.getInstance().getSynchronizedTime();
};

/**
 * Utility function to check if clock sync is needed
 */
export const needsClockSync = (): boolean => {
  return ClockSyncManager.getInstance().needsSync();
};

/**
 * Utility function to perform clock sync if needed
 */
export const ensureClockSync = async (): Promise<void> => {
  const clockSync = ClockSyncManager.getInstance();
  if (clockSync.needsSync()) {
    try {
      await clockSync.syncClock();
    } catch (error) {
      console.warn('[CLOCK_SYNC] Auto-sync failed, proceeding without sync:', error);
    }
  }
};
