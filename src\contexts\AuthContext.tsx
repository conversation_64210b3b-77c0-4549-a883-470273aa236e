import { createContext, useContext, useEffect, useState } from 'react';
import type { ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService, type User } from '@/api/auth.service';

type AuthContextType = {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (email: string, password: string) => Promise<{ requires2FA: boolean }>;
  register: (firstName: string, lastName: string, email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  handleOAuthCallback: (data: any) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  resendVerification: (email: string) => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, _setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Wrapper around _setUser to add logging
  const setUser = (userData: User | null) => {
    console.log('AuthContext - Setting user data:', {
      id: userData?.id,
      email: userData?.email,
      username: userData?.username,
      full_name: userData?.full_name,
      avatar: userData?.avatar,
      profile_picture: userData?.profile_picture,
      registration_type: userData?.registration_type,
      hasPicture: !!(userData?.profile_picture || userData?.avatar)
    });
    _setUser(userData);
  };

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('access_token');
        console.log('Initializing auth, token exists:', !!token);
        if (token) {
          await refreshUser();
        } else {
          console.log('No token found during initialization');
        }
      } catch (error) {
        console.error('Failed to initialize auth', error);
        // Only clear tokens if it's an auth error
        if (error.response?.status === 401) {
          console.log('Auth error during initialization, clearing tokens');
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
        } else {
          console.log('Non-auth error during initialization, keeping tokens');
        }
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string): Promise<{ requires2FA: boolean }> => {
    try {
      console.log('[AUTH_CONTEXT] Starting login process for:', email);
      const response = await authService.login({ email, password });
      console.log('[AUTH_CONTEXT] Login response received:', response);

      // Check if 2FA is required
      if (response.requires_2fa) {
        console.log('[AUTH_CONTEXT] 2FA required, redirecting to verify-2fa page');
        navigate('/verify-2fa', {
          state: {
            tempToken: response.temp_token,
            user: response.user
          }
        });
        return { requires2FA: true };
      }

      // Normal login response
      console.log('[AUTH_CONTEXT] Normal login, setting user and tokens');
      const { user, access_token } = response;
      // Ensure access_token is set before navigation
      if (access_token) {
        localStorage.setItem('access_token', access_token);
      }
      setUser(user);
      return { requires2FA: false };
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const register = async (firstName: string, lastName: string, email: string, password: string) => {
    try {
      await authService.register({ firstName, lastName, email, password });
      // Note: No auto-login after registration - user must verify email first
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  };

  const verifyEmail = async (token: string) => {
    try {
      const { user } = await authService.verifyEmail(token);
      setUser(user);
    } catch (error) {
      console.error('Email verification failed:', error);
      throw error;
    }
  };

  const resendVerification = async (email: string) => {
    try {
      await authService.resendVerification(email);
    } catch (error) {
      console.error('Resend verification failed:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      navigate('/login');
    }
  };

  const refreshUser = async () => {
    try {
      if (authService.isAuthenticated()) {
        const user = await authService.getCurrentUser();
        console.log('Successfully refreshed user:', user);
        setUser(user);
      } else {
        console.log('No token found, setting user to null');
        setUser(null);
      }
    } catch (error) {
      console.error('Failed to refresh user', error);
      // Check if it's a 401 error (token expired/invalid)
      if (error.response?.status === 401) {
        console.log('Token invalid/expired, clearing auth state');
        setUser(null);
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
      } else {
        // For other errors, don't clear the tokens immediately
        // The axios interceptor will handle token refresh if needed
        console.log('Non-auth error, keeping current state');
      }
      throw error;
    }
  };

  const updateToken = async (newToken: string) => {
    try {
      localStorage.setItem('access_token', newToken);
      await refreshUser();
    } catch (error) {
      console.error('Failed to update token:', error);
      throw error;
    }
  };

  const handleOAuthCallback = async (data: any) => {
    try {
      if (data.access_token) localStorage.setItem('access_token', data.access_token);
      if (data.refresh_token) localStorage.setItem('refresh_token', data.refresh_token);
      if (data.user) {
        const userData = data.user;
        console.log('OAuth callback user data:', userData); // Debug log
        
        if (data.provider === 'google' || data.provider === 'google-oauth2') {
          const googleProfile = {
            ...userData,
            // Map profile_picture to both picture and avatar for compatibility
            picture: userData.profile_picture || userData.picture || userData.avatar,
            avatar: userData.profile_picture || userData.picture || userData.avatar,
            // Generate username from available fields
            username: userData.username || userData.full_name || 
                     [userData.given_name, userData.family_name].filter(Boolean).join(' ').trim() || 
                     userData.email?.split('@')[0] || 'User',
            provider: 'google'
          };
          console.log('Processed Google profile:', googleProfile); // Debug log
          setUser(googleProfile);
        } else {
          setUser(userData);
        }
      } else if (data.access_token) {
        await refreshUser();
      }
    } catch (error) {
      console.error('Failed to handle OAuth callback:', error);
      throw error;
    }
  };

  const value = {
    user,
    isAuthenticated: !!user,
    loading,
    login,
    register,
    logout,
    refreshUser,
    updateToken,
    handleOAuthCallback,
    verifyEmail,
    resendVerification,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
