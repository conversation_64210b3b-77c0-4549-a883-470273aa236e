/**
 * Paper Trading Hook
 * 
 * Custom hook for managing paper trading state and operations
 */

import { useState, useEffect, useCallback } from 'react';
import { paperTradingApi } from '../services/paperTradingApi';
import { toast } from '@/components/ui/use-toast';

interface PaperTradingState {
  isPaperMode: boolean;
  isLoading: boolean;
  account: any | null;
  balance: number;
  error: string | null;
}

export const usePaperTrading = () => {
  const [state, setState] = useState<PaperTradingState>({
    isPaperMode: false,
    isLoading: true,
    account: null,
    balance: 0,
    error: null
  });

  // Load initial trading mode
  const loadTradingMode = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      const modeData = await paperTradingApi.getTradingMode();
      
      setState(prev => ({
        ...prev,
        isPaperMode: modeData.paper_trading_enabled,
        isLoading: false
      }));

      // If in paper mode, load account data
      if (modeData.paper_trading_enabled) {
        await loadPaperAccount();
      }
    } catch (error) {
      console.error('Error loading trading mode:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load trading mode',
        isLoading: false
      }));
    }
  }, []);

  // Load paper trading account data
  const loadPaperAccount = useCallback(async () => {
    try {
      const [accountData, balanceData] = await Promise.all([
        paperTradingApi.getPaperAccount(),
        paperTradingApi.getPaperBalance()
      ]);

      setState(prev => ({
        ...prev,
        account: accountData.account,
        balance: balanceData.balance
      }));
    } catch (error) {
      console.error('Error loading paper account:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load paper account'
      }));
    }
  }, []);

  // Switch trading mode
  const switchTradingMode = useCallback(async (paperMode: boolean) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const result = await paperTradingApi.switchTradingMode(paperMode);
      
      if (result.success) {
        if (paperMode) {
          // Load paper account data first, then set paper mode
          await loadPaperAccount();
          setState(prev => ({ ...prev, isPaperMode: paperMode }));

          // Automatically start paper trading signal generation
          console.log('[PaperTrading] Paper mode enabled - signal generation will start automatically');
        } else {
          // Clear paper account data and set live mode
          setState(prev => ({ ...prev, isPaperMode: paperMode, account: null, balance: 0 }));
        }

        // Show success toast
        toast({
          title: "Trading Mode Switched",
          description: `Switched to ${paperMode ? 'Paper Trading' : 'Live Trading'} mode`,
          variant: "default"
        });
      }
    } catch (error) {
      console.error('Error switching trading mode:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to switch trading mode';
      
      setState(prev => ({ ...prev, error: errorMessage }));
      
      // Show error toast
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [loadPaperAccount]);

  // Reset paper trading account
  const resetPaperAccount = useCallback(async (newBalance?: number) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const result = await paperTradingApi.resetPaperAccount(newBalance);
      
      if (result.success) {
        // Reload account data
        await loadPaperAccount();
        
        toast({
          title: "Account Reset",
          description: `Paper trading account reset to $${result.new_balance.toLocaleString()}`,
          variant: "default"
        });
      }
    } catch (error) {
      console.error('Error resetting paper account:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to reset paper account';
      
      setState(prev => ({ ...prev, error: errorMessage }));
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [loadPaperAccount]);

  // Refresh paper trading data
  const refreshData = useCallback(async () => {
    if (state.isPaperMode) {
      await loadPaperAccount();
    }
  }, [state.isPaperMode, loadPaperAccount]);

  // Load initial data on mount
  useEffect(() => {
    loadTradingMode();
  }, [loadTradingMode]);

  return {
    // State
    isPaperMode: state.isPaperMode,
    isLoading: state.isLoading,
    account: state.account,
    balance: state.balance,
    error: state.error,
    
    // Actions
    switchTradingMode,
    resetPaperAccount,
    refreshData,
    loadTradingMode
  };
};

export default usePaperTrading;
