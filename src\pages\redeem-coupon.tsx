import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { toast } from '../components/ui/use-toast';
import { useNavigate } from 'react-router-dom';

interface CouponRedemptionResponse {
  message: string;
  success: boolean;
  coupon: {
    code: string;
    tier_level: number;
    description: string;
  };
  user_tier: {
    previous_tier: number;
    new_tier: number;
    expires_in_days?: number;
  };
  access_token: string;
}

const RedeemCouponPage: React.FC = () => {
  const { user, updateToken } = useAuth();
  const navigate = useNavigate();
  
  const [couponCode, setCouponCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [currentTier, setCurrentTier] = useState<number>(1);

  // Fetch current user tier
  useEffect(() => {
    const fetchCurrentTier = async () => {
      if (!user) return;
      
      try {
        const token = localStorage.getItem('access_token');
        const response = await fetch('/api/trading/tier/status', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          setCurrentTier(data.current_tier || 1);
        }
      } catch (error) {
        console.error('Error fetching tier status:', error);
      }
    };

    fetchCurrentTier();
  }, [user]);

  // Redirect if not logged in
  useEffect(() => {
    if (!user) {
      navigate('/login');
    }
  }, [user, navigate]);

  const handleCouponCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    setCouponCode(value);
  };

  const validateCouponFormat = (code: string): boolean => {
    return /^[A-Z0-9]{4,20}$/.test(code);
  };

  const handleRedeemCoupon = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!couponCode.trim()) {
      toast({
        title: 'Invalid Input',
        description: 'Please enter a coupon code',
        variant: 'destructive'
      });
      return;
    }

    if (!validateCouponFormat(couponCode)) {
      toast({
        title: 'Invalid Format',
        description: 'Coupon code must be 4-20 characters, letters and numbers only',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    setIsValidating(true);

    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/trading/coupon/redeem', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          coupon_code: couponCode
        })
      });

      const data: CouponRedemptionResponse = await response.json();

      if (response.ok && data.success) {
        // Update auth context with new token
        await updateToken(data.access_token);
        
        // Show success message
        const expirationText = data.user_tier.expires_in_days
          ? ` (Valid for ${data.user_tier.expires_in_days} days)`
          : '';

        toast({
          title: 'Coupon Redeemed Successfully!',
          description: `${data.message}${expirationText}`,
          variant: 'default'
        });

        // Clear form
        setCouponCode('');
        setCurrentTier(data.user_tier.new_tier);

        // Redirect to tier page after a short delay
        setTimeout(() => {
          navigate('/tier');
        }, 2000);

      } else {
        toast({
          title: 'Redemption Failed',
          description: data.error || 'Failed to redeem coupon',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error redeeming coupon:', error);
      toast({
        title: 'Network Error',
        description: 'Failed to connect to server. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
      setIsValidating(false);
    }
  };

  const getTierName = (tier: number): string => {
    switch (tier) {
      case 1: return 'Starter (Free)';
      case 2: return 'Pro (Premium)';
      case 3: return 'VIP (Elite)';
      default: return 'Unknown';
    }
  };

  const getTierColor = (tier: number): string => {
    switch (tier) {
      case 1: return 'text-gray-600';
      case 2: return 'text-blue-600';
      case 3: return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mx-auto h-12 w-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
            <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            Redeem Coupon
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Enter your coupon code to upgrade your tier
          </p>
        </div>

        {/* Current Tier Display */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Current Tier:
            </span>
            <span className={`text-sm font-bold ${getTierColor(currentTier)}`}>
              Tier {currentTier} - {getTierName(currentTier)}
            </span>
          </div>
        </div>

        {/* Coupon Form */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <form onSubmit={handleRedeemCoupon} className="space-y-6">
            <div>
              <label htmlFor="coupon-code" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Coupon Code
              </label>
              <div className="relative">
                <input
                  id="coupon-code"
                  type="text"
                  value={couponCode}
                  onChange={handleCouponCodeChange}
                  placeholder="Enter coupon code (e.g., DTABC123)"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                  maxLength={20}
                  disabled={isLoading}
                />
                {isValidating && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                  </div>
                )}
              </div>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Code must be 4-20 characters, letters and numbers only
              </p>
            </div>

            <button
              type="submit"
              disabled={isLoading || !couponCode.trim() || !validateCouponFormat(couponCode)}
              className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Redeeming...
                </>
              ) : (
                <>
                  <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Redeem Coupon
                </>
              )}
            </button>
          </form>
        </div>

        {/* Information Section */}
        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <div className="flex items-start">
            <svg className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="text-sm text-blue-800 dark:text-blue-200">
              <p className="font-medium mb-1">How it works:</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>Enter your coupon code above</li>
                <li>Your tier will be upgraded automatically</li>
                <li>You'll get immediate access to new features</li>
                <li>Each coupon can only be used once</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Back to Tier Page */}
        <div className="mt-6 text-center">
          <button
            onClick={() => navigate('/tier')}
            className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors duration-200"
          >
            ← Back to Tier Management
          </button>
        </div>
      </div>
    </div>
  );
};

export default RedeemCouponPage;
