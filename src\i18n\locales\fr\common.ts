export default {
  "app": {
    "name": "DeepTrade",
    "tagline": "Trading de Cryptomonnaies alimenté par l'IA",
    "description": "Signaux de trading avancés et gestion automatisée de portefeuille",
  },
  "navigation": {
    "dashboard": "Tableau de bord",
    "trading": "Trading",
    "signals": "Signaux",
    "apiCredentials": "Identifiants API",
    "autoTrading": "Trading Automatique",
    "tierManagement": "Gestion des Niveaux",
    "referrals": "Parrainages",
    "accessSecurity": "Accès et Sécurité",
    "settings": "Paramètres",
    "help": "Aide et Support",
    "login": "Connexion",
    "register": "S'inscrire",
    "logout": "Déconnexion",
    "home": "Accueil",
  },
  "auth": {
    "login": {
      "title": "Bon Retour",
      "subtitle": "Entrez votre email et mot de passe pour vous connecter",
      "email": "Email",
      "password": "Mot de passe",
      "emailPlaceholder": "<EMAIL>",
      "passwordPlaceholder": "••••••••",
      "rememberMe": "Se souvenir de moi",
      "forgotPassword": "Mot de passe oublié ?",
      "signIn": "Se connecter",
      "noAccount": "Vous n'avez pas de compte ?",
      "signUp": "S'inscrire",
      "googleSignIn": "Continuer avec Google",
      "orContinueWith": "Ou continuer avec",
      "fillAllFields": "Veuillez remplir tous les champs",
      "loginSuccess": "Connexion réussie !",
      "loginFailed": "Échec de la connexion",
      "loginFailedDescription": "Échec de la connexion",
    },
    "register": {
      "title": "Créer un Compte",
      "subtitle": "Entrez vos informations pour créer un compte",
      "firstName": "Prénom",
      "lastName": "Nom de famille",
      "email": "Adresse e-mail",
      "password": "Mot de passe",
      "confirmPassword": "Confirmer le mot de passe",
      "agreeTerms": "En cliquant sur continuer, vous acceptez nos",
      "createAccount": "Créer un compte",
      "hasAccount": "Vous avez déjà un compte ?",
      "signIn": "Se connecter",
      "orContinueWith": "Ou continuer avec",
      "googleSignUp": "S'inscrire avec Google",
      "termsOfService": "Conditions d'utilisation",
      "privacyPolicy": "Politique de confidentialité",
    },
    "passwordRequirements": {
      "title": "Exigences du mot de passe",
      "length": "Au moins 8 caractères",
      "uppercase": "Une lettre majuscule",
      "lowercase": "Une lettre minuscule",
      "number": "Un chiffre",
      "special": "Un caractère spécial",
    },
    "errors": {
      "invalidCredentials": "Email ou mot de passe invalide",
      "emailRequired": "Email requis",
      "passwordRequired": "Mot de passe requis",
      "passwordTooShort": "Le mot de passe doit contenir au moins 8 caractères",
      "passwordsNotMatch": "Les mots de passe ne correspondent pas",
      "emailInvalid": "Veuillez saisir une adresse email valide",
      "termsRequired": "Vous devez accepter les termes et conditions",
    }
  },
  "dashboard": {
    "title": "Tableau de bord",
    "welcome": "Bon retour !",
    "overview": "Aperçu",
    "trading": "Trading",
    "tier": "Niveau",
    "balance": "Solde",
    "pnlToday": "P&L Aujourd'hui",
    "totalTrades": "Total des Trades",
    "winRate": "Taux de Réussite",
    "activeSignals": "Signaux Actifs",
    "tradingOverview": "Aperçu du Trading",
    "quickActions": "Actions Rapides",
    "viewSignals": "Voir les Signaux",
    "autoTrading": "Trading Automatique",
    "autoTradingDescription": "Exécuter automatiquement les signaux de trading",
    "currentTier": "Niveau Actuel",
    "progressTo": "Progression vers le Niveau {{tier},",
    "yourBenefits": "Vos Avantages",
    "advancedSignals": "Signaux de Trading Avancés",
    "autoTradingFeatures": "Fonctionnalités de Trading Automatique",
    "prioritySupport": "Support Prioritaire",
    "monthlyRate": "Taux Mensuel",
    "profitShare30": "30% de partage des bénéfices",
    "profitShare20": "20% de partage des bénéfices",
    "profitShare10": "10% de partage des bénéfices",
    "verifyingNFT": "Vérification de la propriété NFT...",
    "debt": "Dette",
    "btcPrice": {
      "loading": "BTC/USDT: Chargement...",
      "unavailable": "BTC/USDT: --",
      "connecting": "Connexion...",
      "live": "En Direct",
      "error": "Erreur ({{count}})",
      "24h": "24h",
    },
    "forecast": {
      "title": "Prévision des Prix",
      "refresh": "Actualiser",
      "errorLoading": "Erreur de chargement du graphique",
      "retry": "Réessayer",
      "noData": "Aucune donnée de graphique disponible",
    },
    "riskSettings": {
      "title": "Paramètres de Gestion des Risques",
      "paperTradingTitle": "Paramètres de Gestion des Risques - Trading de Démonstration",
      "loading": "Chargement des paramètres de risque...",
      "investmentPercentage": "Pourcentage d'Investissement",
      "investmentDescription": "Pourcentage du solde à utiliser pour le trading (0% - 10%)",
      "leverage": "Effet de Levier",
      "leverageDescription": "Max: {{max}}x pour Niveau {{tier}} + {{exchange}} {{market},",
      "paperTradingLeverageDesc": "En mode démonstration, vous pouvez utiliser n'importe quel paramètre d'effet de levier à des fins de test",
      "exchange": "Exchange: ",
      "configureCredentials": "Configurer dans les Identifiants API",
      "warningZeroInvestment": "Sélectionnez un pourcentage d'investissement > 0% pour activer le trading",
      "paperTradingNote": "Mode trading de démonstration - aucune limitation de niveau ne s'applique",
      "saveSettings": "Enregistrer les Paramètres",
      "saving": "Enregistrement...",
      "resetToSafe": "Réinitialiser en Mode Sécurisé",
    },
    "activePositions": {
      "title": "Positions Actives",
      "tabs": {
        "all": "toutes",
        "app": "app",
        "external": "externes",
      },
      "refresh": "Actualiser",
      "errorLoading": "Erreur de chargement des positions actives",
      "retry": "Réessayer",
      "noPositions": "Aucune position active.",
      "noPositionsDesc": "Vos positions de trading actives apparaîtront ici une fois que vous commencerez à trader ou activerez le trading automatique.",
      "noAppPositions": "Aucune position active de l'app.",
      "noExternalPositions": "Aucune position externe active.",
      "addCredentialsWarning": "⚠️ Ajoutez des identifiants API pour voir les positions externes",
      "externalError": "❌ Erreur des positions externes: {{error},",
      "externalErrorGeneric": "❌ {{error},",
      "diagnostics": "App: {{app}} | Externes: {{external},",
      "fields": {
        "size": "Taille",
        "entryPrice": "Prix d'Entrée",
        "currentPrice": "Prix Actuel",
        "margin": "Marge",
        "liquidationPrice": "Prix de Liquidation",
        "marginRate": "Taux de Marge",
        "takeProfit": "TP",
        "stopLoss": "SL",
        "roi": "ROI",
        "none": "Aucun",
        "na": "N/A",
      },
      "positionTypes": {
        "appPositions": "Positions de l'App",
        "externalPositions": "Positions Externes",
      }
    },
    "profitShare": {
      "title": "Suivi du Partage des Bénéfices",
      "loading": "Chargement des données de partage des bénéfices...",
      "noData": "Aucune donnée de partage des bénéfices disponible. Commencez à trader pour suivre vos bénéfices.",
      "balanceOverview": "Aperçu du Solde",
      "initialBalance": "Solde Initial:",
      "currentBalance": "Solde Actuel:",
      "netDeposits": "Dépôts Nets:",
      "profitAnalysis": "Analyse des Bénéfices",
      "tradingProfit": "Bénéfice de Trading:",
      "trueProfit": "Bénéfice Réel:",
      "profitPercentage": "% de Bénéfice:",
      "profitShareInfo": "Partage des Bénéfices",
      "tierRate": "Taux Niveau {{tier}}:",
      "amountOwed": "Montant Dû:",
      "status": "Statut:",
      "profitable": "Le compte est rentable - Le partage des bénéfices s'applique aux gains au-dessus du solde initial",
      "notProfitable": "Le compte n'est pas encore rentable - Pas de partage des bénéfices jusqu'à ce que le solde dépasse l'initial + dépôts",
      "paymentRequired": "Paiement Requis",
      "paymentDescription": "Vous avez ${{amount}} en frais de partage des bénéfices à payer.",
      "payNow": "Payer Maintenant",
    },
    "tradingHistory": {
      "title": "Historique de Trading (Trades de l'App Uniquement)",
      "description": "Affichage des trades initiés par la plateforme DeepTrade",
      "loading": "Chargement...",
      "headers": {
        "date": "Date",
        "symbol": "Symbole",
        "side": "Côté",
        "size": "Taille",
        "entry": "Entrée",
        "exit": "Sortie",
        "pnl": "P&L",
        "status": "Statut",
      },
      "noHistory": "Aucun historique de trading trouvé. Commencez à trader pour voir votre historique ici.",
      "pagination": "Page {{page}} sur {{pages}} ({{total}} trades au total)",
      "previous": "Précédent",
      "next": "Suivant",
    },
    "blockedUser": {
      "title": "Trading Bloqué:",
      "message": "Partage des bénéfices impayé pour votre niveau ({{tier}}). Dû: ${{owed}}. Veuillez régler votre solde pour continuer.",
    }
  },
  "trading": {
    "title": "Trading",
    "signals": "Signaux",
    "positions": "Positions",
    "history": "Historique",
    "autoTrading": "Trading Automatique",
    "autoTradingActive": "Trading Automatique Actif",
    "autoTradingDisabled": "Trading Automatique Désactivé",
    "autoTradingActiveDesc": "Les signaux seront exécutés automatiquement",
    "autoTradingDisabledDesc": "Activez pour exécuter automatiquement les signaux de trading",
    "noActiveSignals": "Aucun Signal Actif",
    "waitingForSignals": "En attente de nouvelles opportunités de trading...",
    "enableAutoTrading": "Activez le trading automatique pour recevoir des signaux",
    "refreshSignals": "Actualiser les Signaux",
    "noOpenPositions": "Aucune Position Ouverte",
    "positionsDesc": "Vos positions de trading actives apparaîtront ici",
    "noTradingHistory": "Aucun Historique de Trading",
    "historyDesc": "Vos trades terminés apparaîtront ici",
    "executeTradeButton": "Exécuter le Trade",
    "viewDetailsButton": "Voir les Détails",
    "entry": "Entrée",
    "stopLoss": "Stop Loss",
    "takeProfit": "Take Profit",
    "confidence": "{{value}}% de confiance",
    "strategy": "Stratégie",
    "firstTp": "Premier TP",
    "secondTp": "Deuxième TP",
    "autoMoveStopLoss": "Déplacer automatiquement le SL au seuil de rentabilité après le premier TP",
    "tradingMode": "Mode de Trading",
    "paperMode": "Démo",
    "liveMode": "Live",
    "paperTradingActive": "Trading de Démonstration Actif",
    "liveTradingActive": "Trading Live Actif",
    "paperTradingDesc": "Pratiquez le trading avec des fonds virtuels",
    "liveTradingDesc": "Tradez avec de l'argent réel",
    "virtualBalance": "Solde Virtuel",
    "paperTradingHelp": "Aide Trading de Démonstration",
    "resetAccount": "Réinitialiser le Compte",
    "paperTradingGuide": "Guide du Trading de Démonstration",
    "paperModeTitle": "Mode Démonstration",
    "noProfitSharePaper": "Pas de partage de profits en mode démonstration",
    "paperTradingPositions": "Positions de Trading de Démonstration",
    "paperTradingHistory": "Historique du Trading de Démonstration",
    "simulatedTradingHistory": "Historique de trading simulé",
    "noPaperPositions": "Aucune position de trading de démonstration",
    "enableAutoTradingPaper": "Le trading de démonstration générera automatiquement des positions simulées basées sur les signaux du marché",
    "paperTradingMode": "Mode Trading de Démonstration",
    "autoTradingNotAvailable": "Trading Automatique Non Disponible",
    "autoTradingDisabledPaper": "Le trading automatique contrôle le trading live. Le trading de démonstration génère automatiquement des signaux.",
    "riskManagement": "Gestion des Risques",
    "riskSettings": "Paramètres de Risque",
    "investmentPercentage": "Pourcentage d'Investissement",
    "leverage": "Effet de Levier",
    "maxLeverage": "Effet de Levier Maximum",
    "currentTier": "Niveau Actuel",
    "exchange": "Exchange",
    "accountType": "Type de Compte",
    "marketType": "Type de Marché",
    "saveSettings": "Sauvegarder les Paramètres",
    "resetToSafe": "Réinitialiser en Sécurité",
    "saving": "Sauvegarde...",
    "riskSettingsSaved": "Paramètres de risque sauvegardés avec succès",
    "failedToSaveRiskSettings": "Échec de la sauvegarde des paramètres de risque",
    "position": "position",
    "internal": "Interne",
    "external": "Externe",
    "marketClose": "Fermeture Marché",
    "reverse": "Inverser",
    "editTpSl": "TP/SL",
    "refreshActivePositions": "Actualiser les Positions Actives",
    "youHaveNoPosition": "Vous n'avez aucune position.",
    "noActiveAppPositions": "Aucune position active de l'app trouvée.",
    "noActiveExternalPositions": "Aucune position externe active trouvée.",
    "yourActivePositionsWillAppear": "Vos positions de trading actives apparaîtront ici.",
    "previous": "Précédent",
    "next": "Suivant",
    "learnMore": "En Savoir Plus",
    "paperTradingLabel": "Trading de Démonstration",
    "recentPaperTrades": "Trades de Démonstration Récents",
    "noPaperTrades": "Pas encore de trades de démonstration",
    "paperTradingAutoGenerate": "Le trading de démonstration générera automatiquement des trades simulés basés sur les signaux du marché",
    "activePositionsTitle": "Positions Actives",
    "youHaveNoPositions": "Vous n'avez aucune position.",
    "yourActivePositionsWillAppearHere": "Vos positions de trading actives apparaîtront ici une fois que vous commencerez à trader ou activerez le trading automatique.",
    "max": "Max",
  },
  "common": {
    "loading": "Chargement...",
    "error": "Erreur",
    "success": "Succès",
    "save": "Sauvegarder",
    "cancel": "Annuler",
    "yes": "Oui",
    "no": "Non",
    "active": "Actif",
    "inactive": "Inactif",
    "on": "ACTIVÉ",
    "off": "DÉSACTIVÉ",
    "enabled": "activé",
    "disabled": "désactivé",
    "for": "pour",
    "warning": "Warning",
    "info": "Information",
    "confirm": "Confirmer",
    "edit": "Edit",
    "delete": "Delete",
    "close": "Fermer",
    "back": "Retour",
    "next": "Suivant",
    "previous": "Précédent",
    "submit": "Submit",
    "reset": "Reset",
    "search": "Search",
    "filter": "Filter",
    "sort": "Sort",
    "refresh": "Refresh",
    "enable": "Activer",
    "disable": "Désactiver",
    "toggleOn": "ON",
    "toggleOff": "OFF",
    "ok": "OK",
    "apply": "Appliquer",
    "clear": "Effacer",
    "select": "Sélectionner",
    "upload": "Téléverser",
    "download": "Télécharger",
    "copy": "Copier",
    "copied": "Copié !",
    "share": "Share",
    "print": "Imprimer",
    "export": "Export",
    "import": "Import",
    "and": "et",  },
  "wallet": {
    "connect": "Connecter le Portefeuille",
    "disconnect": "Déconnecter",
    "connected": "Connecté",
    "notConnected": "Non Connecté",
    "connecting": "Connexion...",
    "balance": "Solde",
    "address": "Adresse",
    "solanaBlockchain": "Alimenté par la Blockchain Solana",
    "solanaTooltip": "Construit sur Solana - Blockchain rapide, sécurisée et évolutive",
    "wrongNetwork": "Mauvais Réseau",
    "switchNetwork": "Changer de Réseau",
    "transactionPending": "Transaction en Attente",
    "transactionConfirmed": "Transaction Confirmée",
    "transactionFailed": "Transaction Échouée",
    "connectionError": "Erreur de Connexion",
    "connectionFailed": "Échec de la connexion du portefeuille",
    "connectionRejected": "La connexion du portefeuille a été rejetée",
    "walletNotFound": "Aucun portefeuille Solana trouvé. Veuillez installer un portefeuille comme Phantom.",
    "disconnected": "Portefeuille Déconnecté",
    "disconnectedSuccess": "Votre portefeuille a été déconnecté avec succès.",
    "disconnectError": "Erreur de Déconnexion",
    "disconnectFailed": "Échec de la déconnexion du portefeuille.",
    "confirmDisconnect": "Confirmer la Déconnexion du Portefeuille",
    "confirmDisconnectMessage": "Êtes-vous sûr de vouloir déconnecter votre portefeuille ?",
  },
  "notifications": {
    "autoTradingEnabled": "Le trading automatique a été activé",
    "autoTradingDisabled": "Le trading automatique a été désactivé",
    "settingsSaved": "Paramètres sauvegardés avec succès",
    "passwordChanged": "Mot de passe modifié avec succès",
    "profileUpdated": "Profil mis à jour avec succès",
    "walletConnected": "Portefeuille connecté avec succès",
    "walletDisconnected": "Portefeuille déconnecté",
    "signalExecuted": "Signal de trading exécuté",
    "tradeCompleted": "Transaction terminée avec succès",
    "paymentSuccessful": "Paiement traité avec succès",
    "tierUpgraded": "Niveau mis à jour avec succès",
    "autoTrading": {
      "enabledTitle": "Trading Automatique Activé",
      "enabledDescription": "Le trading automatique a été activé avec succès. Exigence de solde vérifiée.",
      "disabledTitle": "Trading Automatique Désactivé",
      "disabledDescription": "Le trading automatique a été désactivé avec succès.",
      "insufficientBalanceTitle": "Solde Insuffisant",
      "insufficientBalanceDescription": "Solde minimum de {{required}} USDT requis. Votre solde actuel est de {{current}} USDT.",
      "missingCredentialsTitle": "Identifiants API Manquants",
      "missingCredentialsDescription": "Veuillez d'abord ajouter et valider vos identifiants API d'exchange.",
      "balanceVerificationFailedTitle": "Vérification du Solde Échouée",
      "balanceVerificationFailedDescription": "Échec de la vérification du solde du compte. Veuillez vérifier vos identifiants API.",
      "errorTitle": "Erreur de Trading Automatique",
      "errorDescription": "Échec du basculement du trading automatique. Veuillez réessayer.",
    }
  },
  "errors": {
    "generic": "Quelque chose s'est mal passé. Veuillez réessayer.",
    "network": "Erreur réseau. Veuillez vérifier votre connexion.",
    "unauthorized": "Vous n'êtes pas autorisé à effectuer cette action.",
    "forbidden": "Accès refusé.",
    "notFound": "La ressource demandée n'a pas été trouvée.",
    "serverError": "Erreur serveur. Veuillez réessayer plus tard.",
    "validationError": "Veuillez vérifier votre saisie et réessayer.",
    "walletNotConnected": "Veuillez d'abord connecter votre portefeuille.",
    "insufficientBalance": "Solde insuffisant.",
    "transactionFailed": "Transaction échouée. Veuillez réessayer.",
    "apiError": "Erreur API. Contactez le support si cela persiste.",
  },
  "settings": {
    "title": "Paramètres",
    "profile": {
      "title": "Informations du Profil",
      "description": "Mettez à jour les informations de votre profil de compte.",
      "firstName": "Prénom",
      "lastName": "Nom de famille",
      "email": "Adresse Email",
      "updateProfile": "Mettre à jour le Profil",
      "profileUpdated": "Profil mis à jour avec succès",
    },
    "security": {
      "title": "Paramètres de Sécurité",
      "description": "Gérez la sécurité et l'authentification de votre compte.",
      "currentPassword": "Mot de passe actuel",
      "newPassword": "Nouveau mot de passe",
      "confirmPassword": "Confirmer le nouveau mot de passe",
      "changePassword": "Changer le mot de passe",
      "passwordChanged": "Mot de passe modifié avec succès",
      "enable2FA": "Activer l'authentification à deux facteurs",
      "disable2FA": "Désactiver l'authentification à deux facteurs",
      "twoFactorEnabled": "Authentification à deux facteurs activée",
      "twoFactorDisabled": "Authentification à deux facteurs désactivée",
    },
    "language": {
      "title": "Langue et Préférences",
      "description": "Personnalisez votre langue et vos préférences d'affichage.",
      "selectLanguage": "Langue",
      "languageDescription": "Choisissez votre langue préférée pour l'interface.",
    },
    "account": {
      "title": "Gestion du Compte",
      "description": "Gérez les paramètres et données de votre compte.",
      "deleteAccount": "Supprimer le Compte",
      "deleteAccountWarning": "Cette action ne peut pas être annulée. Cela supprimera définitivement votre compte et supprimera vos données de nos serveurs.",
      "confirmDelete": "Tapez 'DELETE' pour confirmer",
      "accountDeleted": "Compte supprimé avec succès"    }
  },
  "accessSecurity": {
    "title": "Accès et Sécurité",
    "subtitle": "Surveillez la sécurité de votre compte et l'activité de connexion",
    "totalLogins": "Total des Connexions",
    "successRate": "Taux de Réussite",
    "uniqueIPs": "IPs Uniques",
    "twoFactorStatus": "Statut 2FA",
    "enabled": "Activé",
    "disabled": "Désactivé",
    "currentSession": "Session Actuelle",
    "currentSessionDescription": "Informations sur votre session de connexion actuelle",
    "recentLoginActivity": "Activité de Connexion Récente",
    "recentLoginDescription": "Vos tentatives de connexion et sessions récentes",
    "refresh": "Actualiser",
    "ipAddress": "Adresse IP",
    "timestamp": "Horodatage",
    "status": "Statut",
    "userAgent": "Agent Utilisateur",
    "successful": "Réussi",
    "failed": "Échoué",
    "noLoginActivity": "Aucune activité de connexion trouvée",
    "currentDevice": "Appareil Actuel",
    "location": "Emplacement",
    "sessionStart": "Début de Session",
  },
  "tiers": {
    "title": "Gestion des Niveaux",
    "currentTier": "Niveau Actuel",
    "choosePlan": "Choisissez Votre Plan",
    "tier1": {
      "name": "Niveau 1",
      "subtitle": "Gratuit",
      "description": "Parfait pour commencer avec des signaux de trading de base",
      "features": [
        "Signaux de trading de base",
        "Exécution manuelle des transactions",
        "Support communautaire",
        "Analyse de marché de base"
      ]
    },
    "tier2": {
      "name": "Niveau 2",
      "subtitle": "Accès 30 jours",
      "description": "Fonctionnalités avancées pour les traders sérieux",
      "features": [
        "Signaux de trading IA avancés",
        "Capacités de trading automatique",
        "Support prioritaire",
        "Analyses avancées",
        "Outils de gestion des risques"
      ]
    },
    "tier3": {
      "name": "Niveau 3",
      "subtitle": "NFT Requis",
      "description": "Niveau premium avec des fonctionnalités exclusives",
      "features": [
        "Signaux de trading premium",
        "Trading automatique avancé",
        "Support VIP",
        "Insights de marché exclusifs",
        "Stratégies personnalisées"
      ]
    },
    "activate": "Activer",
    "current": "Actuel",
    "paymentRequired": "Paiement Requis",
    "nftRequired": "NFT Requis",
    "membershipStatus": "Statut d'Adhésion",
    "active": "Actif",
    "expired": "Expiré",
    "daysRemaining": "Jours restants",
    "expiresOn": "Expire le",
  },
  "apiCredentials": {
    "title": "Identifiants API",
    "description": "Gérez vos identifiants API d'échange pour le trading",
    "addCredentials": "Ajouter des Identifiants API",
    "exchange": "Échange",
    "exchanger": "Échangeur",
    "apiKey": "Clé API",
    "apiSecret": "Secret API",
    "secretKey": "Clé Secrète",
    "passphrase": "Phrase de passe",
    "testConnection": "Tester la Connexion",
    "save": "Sauvegarder les Identifiants",
    "edit": "Modifier",
    "delete": "Supprimer",
    "status": "Statut",
    "connected": "Connecté",
    "disconnected": "Déconnecté",
    "invalid": "Invalide",
    "active": "Actif",
    "testing": "Test en cours...",
    "connectionSuccess": "Connexion réussie",
    "connectionFailed": "Connexion échouée",
    "credentialsAdded": "Identifiants API ajoutés avec succès",
    "credentialsUpdated": "Identifiants API mis à jour avec succès",
    "credentialsDeleted": "Identifiants API supprimés avec succès",
    "connectedTo": "Connecté à",
    "connectionNeedsAttention": "Connexion à",
    "needsAttention": "nécessite une attention",
    "lastUpdated": "Dernière mise à jour",
    "disconnect": "Déconnecter",
    "disconnecting": "Déconnexion...",
    "setAsActive": "Définir comme Actif",
    "activating": "Activation...",
    "autoTrading": "Trading Automatique",
    "enableAutoTradingDesc": "Activer le trading automatisé pour cet échange",
    "add": "Ajouter",
    "update": "Mettre à jour",
    "connectionValidated": "Connexion validée !",
    "requiredMarketType": "Type de Marché Requis",
    "marketTypes": {
      "futures": "Futures",
      "spot": "Spot",
      "binanceNote": "Trading de futures - effet de levier jusqu'à 125x disponible",
      "binanceUsNote": "Trading spot avec marge - effet de levier jusqu'à 3x disponible",
      "krakenNote": "Trading de futures - effet de levier jusqu'à 100x disponible",
      "bybitNote": "Trading de futures - effet de levier jusqu'à 100x disponible",
      "hyperliquidNote": "Trading de futures perpétuels - effet de levier jusqu'à 50x disponible",
      "standardNote": "Trading spot standard",
    },
    "errors": {
      "loadFailed": "Échec du chargement des identifiants",
      "keysRequired": "Clé API et Clé Secrète requises",
      "tierUndetermined": "Impossible de déterminer le niveau utilisateur",
      "tier1Limit": "Niveau 1 permet seulement un identifiant API",
      "tier2PerCex": "Niveau 2 permet seulement un identifiant par CEX",
      "tier2Limit": "Niveau 2 permet jusqu'à deux identifiants (un par CEX)",
      "tier3Limit": "Niveau 3 permet seulement un identifiant pour tous les CEX",
      "saveFailed": "Échec de la sauvegarde",
      "validationFailed": "Échec de la validation",
      "activationFailed": "Échec de l'activation",
      "deactivationFailed": "Échec de la désactivation",
      "noCredentials": "Aucun identifiant trouvé pour cet échange",
      "autoTradingUpdateFailed": "Échec de la mise à jour du trading automatique",
      "autoTradingUpdateFailedDesc": "Impossible de mettre à jour les paramètres de trading automatique",
    },
    "success": {
      "credentialsSaved": "Identifiants sauvegardés",
      "connectionSuccessful": "Connexion réussie",
      "activated": "Activé",
      "deactivated": "Désactivé",
      "autoTradingEnabled": "Trading automatique activé",
      "autoTradingDisabled": "Trading automatique désactivé",
    }
  },
  "referrals": {
    "title": "Tableau de Bord des Parrainages",
    "yourReferralCode": "Votre Code de Parrainage",
    "referralLink": "Lien de Parrainage",
    "copyLink": "Copier le Lien",
    "linkCopied": "Lien copié dans le presse-papiers",
    "totalReferrals": "Total des Parrainages",
    "activeReferrals": "Parrainages Actifs",
    "totalEarnings": "Gains Totaux",
    "pendingPayouts": "Paiements en Attente",
    "referralHistory": "Historique des Parrainages",
    "noReferrals": "Pas encore de parrainages",
    "inviteFriends": "Invitez des amis et gagnez des récompenses",
  },
  "help": {
    "title": "Aide et Support",
    "faq": "FAQ",
    "legal": "Légal",
    "contact": "Contact",
    "searchFAQ": "Rechercher dans la FAQ...",
    "categories": {
      "general": "Général",
      "trading": "Trading",
      "tiers": "Niveaux",
      "setup": "Configuration",
      "security": "Sécurité",
      "billing": "Facturation",
    },
    "contactSupport": "Contacter le Support",
    "email": "Email",
    "subject": "Sujet",
    "message": "Message",
    "sendMessage": "Envoyer le Message",
    "messageSent": "Message envoyé avec succès",
  },
  "paperTradingHelp": {
    "title": "Guide du Trading de Démonstration",
    "whatIsPaperTrading": "Qu'est-ce que le Trading de Démonstration ?",
    "whatIsPaperTradingDesc": "Le trading de démonstration est un environnement de trading simulé qui vous permet de pratiquer des stratégies de trading en utilisant de l'argent virtuel au lieu de fonds réels. C'est le moyen parfait de tester notre système de trading IA sans aucun risque financier.",
    "keyBenefits": "Avantages Clés",
    "riskFree": "Apprentissage Sans Risque",
    "riskFreeDesc": "Pratiquez avec des fonds virtuels - aucun argent réel en risque",
    "realMarketData": "Données de Marché Réelles",
    "realMarketDataDesc": "Expérimentez les conditions de marché en direct et les mouvements de prix",
    "aiTradingSystem": "Système de Trading IA",
    "aiTradingSystemDesc": "Testez nos signaux IA avancés dans un environnement sûr",
    "buildConfidence": "Construire la Confiance",
    "buildConfidenceDesc": "Développez vos compétences de trading avant d'utiliser de l'argent réel",
    "howItWorks": "Comment ça Marche",
    "step1": "Basculer en Mode Démonstration",
    "step1Desc": "Basculez le commutateur de mode de trading pour activer le trading de démonstration",
    "step2": "Solde Virtuel",
    "step2Desc": "Commencez avec un solde virtuel de 10 000 $ (peut être réinitialisé)",
    "step3": "Signaux IA",
    "step3Desc": "Notre IA génère des signaux de trading basés sur des données de marché réelles",
    "step4": "Trades Simulés",
    "step4Desc": "Les trades sont exécutés virtuellement - suivez votre performance",
    "importantNotes": "Notes Importantes",
    "noRealMoney": "Pas d'Argent Réel",
    "noRealMoneyDesc": "Le trading de démonstration utilise uniquement des fonds virtuels. Aucun profit ou perte réel ne se produit.",
    "noRealFees": "Pas de Frais Réels",
    "noRealFeesDesc": "Les frais de trading et le slippage sont simulés pour une expérience réaliste.",
    "performanceTracking": "Suivi des Performances",
    "performanceTrackingDesc": "Toutes les statistiques et analyses sont basées uniquement sur des trades simulés.",
    "readyToStart": "Prêt à Commencer ?",
    "readyToStartDesc": "Basculez le commutateur \"Mode de Trading\" sur \"Démonstration\" et commencez à pratiquer avec des fonds virtuels dès aujourd'hui !",
    "buildConfidenceFooter": "💪 Construisez votre confiance avec le trading de démonstration avant de risquer de l'argent réel !",
    "resetModal": {
      "title": "Réinitialiser le Compte de Trading de Démonstration",
      "warning": "Attention : Cette action ne peut pas être annulée",
      "warningDesc": "Cela supprimera définitivement tout votre historique de trading de démonstration et réinitialisera votre solde virtuel.",
      "resetLimitReached": "Limite de Réinitialisation Quotidienne Atteinte",
      "resetLimitReachedDesc": "Vous avez utilisé les 3 réinitialisations pour aujourd'hui. La limite se remet à zéro à minuit UTC.",
      "resetsRemaining": "Réinitialisations restantes aujourd'hui : {{count},",
      "currentBalance": "Solde Actuel : {{balance}} $",
      "resetToDefault": "Réinitialiser à 10 000 $ par défaut",
      "customBalance": "Définir un solde personnalisé",
      "customBalanceDesc": "Entrez un solde de départ personnalisé (minimum 1 000 $)",
      "confirmationRequired": "Tapez 'RESET' pour confirmer",
      "confirmationPlaceholder": "Tapez RESET ici...",
      "confirmationRequiredError": "Veuillez taper 'RESET' pour confirmer",
      "invalidBalance": "Solde Invalide",
      "invalidBalanceDesc": "Veuillez entrer un montant de solde positif valide",
      "resetting": "Réinitialisation...",
      "resetButton": "Réinitialiser le Compte",
    },
    "howItWorksModal": {
      "title": "Comment Fonctionne le Trading de Démonstration",
      "step1": "Commencez avec un solde virtuel de 10 000 $ (configurable)",
      "step2": "Activez le trading automatique pour que l'IA exécute les trades automatiquement",
      "step3": "Surveillez les performances et ajustez les paramètres de risque selon les besoins",
      "step4": "Passez au trading en direct lorsque vous avez confiance dans le système",
    },
    "importantNotesDetails": {
      "noRealMoneyTitle": "Pas d'Argent Réel :",
      "noRealMoneyDesc": "Le trading de démonstration utilise uniquement des fonds virtuels. Aucun profit ou perte réel ne se produit.",
      "noFeesTitle": "Pas de Frais :",
      "noFeesDesc": "Le trading de démonstration ne génère pas de frais de partage des bénéfices car aucun profit réel n'est réalisé.",
      "resetLimitTitle": "Limite de Réinitialisation :",
      "resetLimitDesc": "Vous pouvez réinitialiser votre compte de démonstration jusqu'à 3 fois par jour.",
    },
    "benefits": {
      "title": "Avantages du Trading de Démonstration",
      "riskFreeLearning": "Apprentissage Sans Risque",
      "riskFreeLearningDesc": "Pratiquez sans perdre d'argent réel tout en apprenant comment fonctionne notre système d'IA",
      "strategyTesting": "Test de Stratégies",
      "strategyTestingDesc": "Testez différents paramètres de risque et voyez comment ils fonctionnent dans le temps",
      "realMarketData": "Données de Marché Réelles",
      "realMarketDataDesc": "Utilise les prix du marché en direct et de vraies prévisions ML pour une expérience authentique",
    },
    "gettingStarted": {
      "title": "Prêt à Commencer ?",
      "description": "Basculez l'interrupteur \"Mode de Trading\" sur \"Démonstration\" et commencez à pratiquer avec des fonds virtuels dès aujourd'hui !",
      "buildConfidenceFooter": "💪 Construisez votre confiance avec le trading de démonstration avant de risquer de l'argent réel !",
    },
    "perfectForBeginners": "💡 Parfait pour les débutants et les traders expérimentés qui veulent tester de nouvelles stratégies !",
    "availableToAllTitle": "Disponible pour Tous :",
    "availableToAllDesc": "Le trading de démonstration est disponible pour tous les utilisateurs quel que soit leur niveau de tier.",
    "gotItThanks": "Compris, merci !",
  },
  "paperTradingAnalytics": {
    "title": "Analyse du Trading de Démonstration",
    "refresh": "Actualiser",
    "periods": {
      "7d": "7J",
      "30d": "30J",
      "90d": "90J",
    },
    "metrics": {
      "totalPnl": "P&L Total",
      "winRate": "Taux de Réussite",
      "totalTrades": "Trades Totaux",
      "profitFactor": "Facteur de Profit",
    },
    "tradeBreakdown": {
      "title": "Répartition des Trades",
      "winningTrades": "Trades Gagnants",
      "losingTrades": "Trades Perdants",
    },
    "averagePerformance": {
      "title": "Performance Moyenne",
      "averageWin": "Gain Moyen",
      "averageLoss": "Perte Moyenne",
      "riskRewardRatio": "Ratio Risque/Récompense",
    },
    "balanceHistory": {
      "title": "Historique du Solde",
      "chartVisualization": "Visualisation du graphique de solde",
      "current": "Actuel",
    },
    "errors": {
      "loadFailed": "Échec du chargement des données d'analyse",
    }
  },
  "paperTradingDashboard": {
    "performance": {
      "title": "Performance du Trading de Démonstration",
      "refresh": "Actualiser",
      "reset": "Réinitialiser",
      "totalReturn": "Rendement Total",
    },
    "analytics": {
      "thirtyDay": "Analyse 30 Jours",
    },
    "errors": {
      "loadFailed": "Échec du chargement des données de trading de démonstration",
    }
  }
};
