import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toastSuccess, toastError } from '@/components/ui/use-toast';
import { useTranslation } from '@/hooks/useTranslation';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { t } = useTranslation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toastError({
        title: t('common.error'),
        description: 'Email address is required',
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/auth/forgot-password', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ email: email.trim().toLowerCase() })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to send password reset email');
      }

      setIsSubmitted(true);
      toastSuccess({
        title: 'Reset Email Sent',
        description: data.message,
      });

    } catch (error) {
      console.error('Forgot password failed:', error);
      toastError({
        title: 'Request Failed',
        description: error instanceof Error ? error.message : 'Failed to send password reset email',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Check Your Email</h1>
          <p className="text-sm text-muted-foreground">
            If an account with this email exists, you will receive a password reset link.
          </p>
        </div>
        
        <div className="grid gap-6">
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg dark:bg-green-900/20 dark:border-green-800">
            <h3 className="font-medium text-green-900 dark:text-green-100 mb-2">Email Sent</h3>
            <p className="text-sm text-green-800 dark:text-green-200 mb-3">
              We've sent a password reset link to <strong>{email}</strong> if an account exists.
            </p>
            <div className="text-sm text-green-800 dark:text-green-200">
              <p className="mb-1">• Check your spam/junk folder if you don't see the email</p>
              <p className="mb-1">• The reset link expires in 1 hour</p>
              <p>• You can request another reset if needed</p>
            </div>
          </div>
          
          <div className="flex flex-col space-y-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsSubmitted(false);
                setEmail('');
              }}
              className="w-full"
            >
              Send Another Reset Email
            </Button>
            
            <Button asChild variant="ghost" className="w-full">
              <Link to="/login">Back to Login</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">Forgot Password</h1>
        <p className="text-sm text-muted-foreground">
          Enter your email address and we'll send you a link to reset your password.
        </p>
      </div>
      
      <div className="grid gap-6">
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                placeholder="Enter your email address"
                type="email"
                autoCapitalize="none"
                autoComplete="email"
                autoCorrect="off"
                disabled={isLoading}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            
            <Button disabled={isLoading} className="w-full">
              {isLoading ? 'Sending...' : 'Send Reset Link'}
            </Button>
          </div>
        </form>
        
        <div className="flex flex-col space-y-2 text-center">
          <Button asChild variant="ghost" className="w-full">
            <Link to="/login">Back to Login</Link>
          </Button>
          
          <div className="text-sm text-muted-foreground space-y-2">
            <p>
              Don't have an account?{' '}
              <Link to="/signup" className="underline underline-offset-4 hover:text-primary">
                Sign up
              </Link>
            </p>
            <p>
              Lost access to your 2FA device?{' '}
              <Link to="/2fa-reset-request" className="underline underline-offset-4 hover:text-primary">
                Request 2FA reset
              </Link>
            </p>
          </div>
        </div>
      </div>
      
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
        <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Security Note</h3>
        <div className="text-sm text-blue-800 dark:text-blue-200">
          <p className="mb-1">• Password reset is only available for traditional accounts</p>
          <p className="mb-1">• Google OAuth users should use "Sign in with Google"</p>
          <p>• Reset links expire after 1 hour for security</p>
        </div>
      </div>
    </div>
  );
}
