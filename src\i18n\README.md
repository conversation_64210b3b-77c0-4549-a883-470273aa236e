# DeepTrade Internationalization (i18n) System

This document provides comprehensive guidance for developers on how to work with the DeepTrade internationalization system.

## Overview

DeepTrade uses a custom i18n system built on top of react-i18next, supporting 8 languages:
- English (en) - Default
- Spanish (es)
- Portuguese (pt)
- Korean (ko)
- Japanese (ja)
- German (de)
- French (fr)
- Chinese (zh)

## Architecture

### File Structure
```
frontend/src/i18n/
├── index.ts                 # Main i18n configuration
├── locales/
│   ├── en/common.ts        # English translations
│   ├── es/common.ts        # Spanish translations
│   ├── pt/common.ts        # Portuguese translations
│   ├── ko/common.ts        # Korean translations
│   ├── ja/common.ts        # Japanese translations
│   ├── de/common.ts        # German translations
│   ├── fr/common.ts        # French translations
│   └── zh/common.ts        # Chinese translations
└── README.md               # This documentation
```

### Key Components
- **Translation Hook**: `useTranslation()` - Enhanced hook with formatting utilities
- **Language Selector**: `LanguageSelector.tsx` - UI component for language switching
- **Configuration**: `i18n/index.ts` - Main configuration and utilities

## Usage Guide

### 1. Using Translations in Components

```typescript
import { useTranslation } from '@/hooks/useTranslation';

function MyComponent() {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('navigation.dashboard')}</h1>
      <p>{t('dashboard.welcome')}</p>
    </div>
  );
}
```

### 2. Translation Key Structure

Translation keys follow a hierarchical structure using dot notation:

```typescript
// Good examples:
t('navigation.dashboard')
t('auth.login.title')
t('settings.profile.firstName')
t('errors.network')

// Avoid:
t('Dashboard')
t('loginTitle')
t('firstName')
```

### 3. Adding New Translation Keys

#### Step 1: Add to English (en/common.ts)
```typescript
export default {
  // ... existing translations
  "newFeature": {
    "title": "New Feature",
    "description": "This is a new feature description",
    "buttons": {
      "save": "Save",
      "cancel": "Cancel"
    }
  }
};
```

#### Step 2: Add to All Other Languages
Update the same structure in all language files:
- `es/common.ts` (Spanish)
- `pt/common.ts` (Portuguese)
- `ko/common.ts` (Korean)
- `ja/common.ts` (Japanese)
- `de/common.ts` (German)
- `fr/common.ts` (French)
- `zh/common.ts` (Chinese)

#### Step 3: Use in Components
```typescript
function NewFeatureComponent() {
  const { t } = useTranslation();
  
  return (
    <div>
      <h2>{t('newFeature.title')}</h2>
      <p>{t('newFeature.description')}</p>
      <button>{t('newFeature.buttons.save')}</button>
      <button>{t('newFeature.buttons.cancel')}</button>
    </div>
  );
}
```

## Translation Categories

### Current Categories
- `auth` - Authentication related text
- `navigation` - Navigation menu items
- `dashboard` - Dashboard content
- `trading` - Trading interface
- `settings` - Settings pages
- `tiers` - Tier management
- `apiCredentials` - API credentials management
- `referrals` - Referral system
- `help` - Help and support
- `common` - Common UI elements
- `wallet` - Wallet related text
- `notifications` - Toast notifications
- `errors` - Error messages

### Adding New Categories
When adding a new feature, create a new category:

```typescript
export default {
  // ... existing categories
  "newCategory": {
    "title": "New Category Title",
    "items": {
      "item1": "Item 1",
      "item2": "Item 2"
    }
  }
};
```

## Advanced Features

### 1. Formatting Utilities

The `useTranslation` hook provides additional formatting utilities:

```typescript
const { t, formatCurrency, formatNumber, formatDate } = useTranslation();

// Currency formatting
const price = formatCurrency(1234.56); // "$1,234.56"

// Number formatting
const number = formatNumber(1234567); // "1,234,567"

// Date formatting
const date = formatDate(new Date()); // Localized date format
```

### 2. Pluralization

For content that needs pluralization:

```typescript
// In translation file:
"items": {
  "count_0": "No items",
  "count_1": "{{count}} item",
  "count_other": "{{count}} items"
}

// In component:
t('items.count', { count: itemCount })
```

### 3. Context-Sensitive Translations

For words that change meaning based on context:

```typescript
// In translation file:
"button": {
  "save_form": "Save",
  "save_file": "Save File"
}

// In component:
t('button.save_form')  // "Save"
t('button.save_file')  // "Save File"
```

## Adding a New Language

### Step 1: Update Language Configuration
In `i18n/index.ts`, add the new language to the languages array:

```typescript
export const languages = [
  // ... existing languages
  { code: 'it', name: 'Italian', flag: 'IT', nativeName: 'Italiano' },
];
```

### Step 2: Create Translation File
Create `locales/it/common.ts` with the complete translation structure.

### Step 3: Import and Configure
In `i18n/index.ts`:

```typescript
import itCommon from './locales/it/common';

const resources = {
  // ... existing resources
  it: { common: itCommon },
};
```

### Step 4: Add Flag Icon
Ensure the flag icon is available in the FlagIcon component.

## Best Practices

### 1. Translation Keys
- Use descriptive, hierarchical keys
- Keep keys consistent across features
- Avoid overly long keys
- Use camelCase for multi-word keys

### 2. Translation Content
- Keep translations concise but clear
- Maintain consistent tone across languages
- Consider cultural context
- Test with longer translations (German, Finnish)

### 3. Component Integration
- Always use the translation hook
- Avoid hardcoded strings
- Consider text length variations
- Test responsive design with translations

### 4. Maintenance
- Update all languages when adding new keys
- Use development mode to catch missing keys
- Regularly review and update translations
- Consider professional translation services

## Development Tools

### Missing Key Detection
In development mode, missing translation keys are logged to the console:

```
Missing translation key: newFeature.title for language: es
```

### Language Switching
Use the LanguageSelector component in different variants:
- `navbar` - For navigation bar
- `settings` - For settings page
- `mobile` - For mobile navigation

## Testing

### Manual Testing
1. Switch between all supported languages
2. Check text wrapping and layout
3. Verify mobile responsiveness
4. Test with longer text (German, Finnish)

### Automated Testing
Consider adding tests for:
- Translation key existence
- Component rendering with different languages
- Language persistence
- Responsive layout with various text lengths

## Troubleshooting

### Common Issues
1. **Missing translations**: Check console for missing key warnings
2. **Layout breaks**: Test with longer languages (German)
3. **Persistence issues**: Verify localStorage is working
4. **Component not updating**: Ensure useTranslation hook is used

### Debug Mode
Enable debug mode in development:

```typescript
// In i18n/index.ts
debug: process.env.NODE_ENV === 'development',
```

## Future Enhancements

### Potential Improvements
1. **Translation Management UI**: Admin interface for managing translations
2. **Automatic Translation**: Integration with translation services
3. **Context Detection**: Automatic context-sensitive translations
4. **Performance**: Lazy loading of translation files
5. **Validation**: Automated translation completeness checks

### Scalability Considerations
- Consider splitting large translation files by feature
- Implement lazy loading for better performance
- Add translation validation in CI/CD pipeline
- Consider using translation management platforms

---

For questions or issues with the i18n system, please refer to the react-i18next documentation or contact the development team.
